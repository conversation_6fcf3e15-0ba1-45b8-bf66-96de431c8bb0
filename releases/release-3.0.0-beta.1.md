---
title: TiDB 3.0.0 Beta.1 Release Notes
summary: TiDB 3.0.0 Beta.1 was released on March 26, 2019, with improved stability, usability, features, SQL optimizer, statistics, and execution engine. The release includes support for various SQL functions, privilege management, server enhancements, DDL improvements, and PD and TiKV optimizations. Tools like TiDB Binlog, Lightning, and data replication comparison tool have also been updated with new features and improvements.
---

# TiDB 3.0.0 Beta.1 Release Notes

Release Date: March 26, 2019

TiDB version: 3.0.0-beta.1

TiDB Ansible version: 3.0.0-beta.1

## Overview

On March 26, 2019, TiDB 3.0.0 Beta.1 is released. The corresponding TiDB Ansible version is 3.0.0 Beta.1. Compared with TiDB 3.0.0 Beta, this release has greatly improved the stability, usability, features, the SQL optimizer, statistics, and the execution engine.

## TiDB

+ SQL Optimizer
    - Support calculating the Cartesian product by using `Sort Merge Join` [#9032](https://github.com/pingcap/tidb/pull/9037)
    - Support Skyline Pruning, with some rules to prevent the execution plan from relying too heavily on statistics [#9337](https://github.com/pingcap/tidb/pull/9337)
    + Support Window Functions
        - `NTILE` [#9682](https://github.com/pingcap/tidb/pull/9682)
        - `LEAD` and `LAG` [#9672](https://github.com/pingcap/tidb/pull/9672)
        - `PERCENT_RANK` [#9671](https://github.com/pingcap/tidb/pull/9671)
        - `NTH_VALUE` [#9596](https://github.com/pingcap/tidb/pull/9596)
        - `CUME_DIST` [#9619](https://github.com/pingcap/tidb/pull/9619)
        - `FIRST_VALUE` and `LAST_VALUE` [#9560](https://github.com/pingcap/tidb/pull/9560)
        - `RANK` and `DENSE_RANK` [#9500](https://github.com/pingcap/tidb/pull/9500)
        - `RANGE FRAMED` [#9450](https://github.com/pingcap/tidb/pull/9450)
        - `ROW FRAMED` [#9358](https://github.com/pingcap/tidb/pull/9358)
        - `ROW NUMBER` [#9098](https://github.com/pingcap/tidb/pull/9098)
    - Add a type of statistic that indicates the order correlation between columns and the handle column [#9315](https://github.com/pingcap/tidb/pull/9315)
+ SQL Execution Engine
    + Add built-in functions
        - `JSON_QUOTE` [#7832](https://github.com/pingcap/tidb/pull/7832)
        - `JSON_ARRAY_APPEND` [#9609](https://github.com/pingcap/tidb/pull/9609)
        - `JSON_MERGE_PRESERVE` [#8931](https://github.com/pingcap/tidb/pull/8931)
        - `BENCHMARK` [#9252](https://github.com/pingcap/tidb/pull/9252)
        - `COALESCE` [#9087](https://github.com/pingcap/tidb/pull/9087)
        - `NAME_CONST` [#9261](https://github.com/pingcap/tidb/pull/9261)
    - Optimize the Chunk size based on the query context, to reduce the execution time of SQL statements and resources consumption of the cluster [#6489](https://github.com/pingcap/tidb/issues/6489)
+ Privilege management
    - Support `SET ROLE` and `CURRENT_ROLE` [#9581](https://github.com/pingcap/tidb/pull/9581)
    - Support `DROP ROLE` [#9616](https://github.com/pingcap/tidb/pull/9616)
    - Support `CREATE ROLE` [#9461](https://github.com/pingcap/tidb/pull/9461)
+ Server
    - Add the `/debug/zip` HTTP interface to get information of the current TiDB instance [#9651](https://github.com/pingcap/tidb/pull/9651)
    - Support the `show pump status` and `show drainer status` SQL statements to check the Pump or Drainer status [#9456](https://github.com/pingcap/tidb/pull/9456)
    - Support modifying the Pump or Drainer status by using SQL statements [#9789](https://github.com/pingcap/tidb/pull/9789)
    - Support adding HASH fingerprints to SQL text for easy tracking of slow SQL statements [#9662](https://github.com/pingcap/tidb/pull/9662)
    - Add the `log_bin` system variable ("0" by default) to control the enabling state of binlog; only support checking the state currently [#9343](https://github.com/pingcap/tidb/pull/9343)
    - Support managing the sending binlog strategy by using the configuration file [#9864](https://github.com/pingcap/tidb/pull/9864)
    - Support querying the slow log by using the `INFORMATION_SCHEMA.SLOW_QUERY` memory table [#9290](https://github.com/pingcap/tidb/pull/9290)
    - Change the MySQL version displayed in TiDB from 5.7.10 to 5.7.25 [#9553](https://github.com/pingcap/tidb/pull/9553)
    - Unify the [log format](https://github.com/tikv/rfcs/blob/master/text/0018-unified-log-format.md) for easy collection and analysis by tools
    - Add the `high_error_rate_feedback_total` monitoring item to record the difference between the actual data volume and the estimated data volume based on statistics [#9209](https://github.com/pingcap/tidb/pull/9209)
    - Add the QPS monitoring item in the database dimension, which can be enabled by using a configuration item [#9151](https://github.com/pingcap/tidb/pull/9151)
+ DDL
    - Add the `ddl_error_count_limit` global variable ("512" by default) to limit the number of DDL task retries (If this number exceeds the limit, the DDL task is canceled) [#9295](https://github.com/pingcap/tidb/pull/9295)
    - Support ALTER ALGORITHM `INPLACE`/`INSTANT` [#8811](https://github.com/pingcap/tidb/pull/8811)
    - Support the `SHOW CREATE VIEW` statement [#9309](https://github.com/pingcap/tidb/pull/9309)
    - Support the `SHOW CREATE USER` statement [#9240](https://github.com/pingcap/tidb/pull/9240)

## PD

+ Unify the [log format](https://github.com/tikv/rfcs/blob/master/text/0018-unified-log-format.md) for easy collection and analysis by tools
+ Simulator
    - Support different heartbeat intervals in different stores [#1418](https://github.com/pingcap/pd/pull/1418)
    - Add a case about importing data [#1263](https://github.com/pingcap/pd/pull/1263)
+ Make hotspot scheduling configurable [#1412](https://github.com/pingcap/pd/pull/1412)
+ Add the store address as the dimension monitoring item to replace the previous Store ID [#1429](https://github.com/pingcap/pd/pull/1429)
+ Optimize the `GetStores` overhead to speed up the Region inspection cycle [#1410](https://github.com/pingcap/pd/pull/1410)
+ Add an interface to delete the Tombstone Store [#1472](https://github.com/pingcap/pd/pull/1472)

## TiKV

+ Optimize the Coprocessor calculation execution framework and implement the TableScan section, with the Single TableScan performance improved by 5% ~ 30%
    - Implement the definition of the `BatchRows` row and the `BatchColumn` column [#3660](https://github.com/tikv/tikv/pull/3660)
    - Implement `VectorLike` to support accessing encoded and decoded data in the same way [#4242](https://github.com/tikv/tikv/pull/4242)
    - Define the `BatchExecutor` to interface and implement the way of converting requests to `BatchExecutor` [#4243](https://github.com/tikv/tikv/pull/4243)
    - Implement transforming the expression tree into the RPN format [#4329](https://github.com/tikv/tikv/pull/4329)
    - Implement the `BatchTableScanExecutor` vectorization operator to accelerate calculation [#4351](https://github.com/tikv/tikv/pull/4351)
+ Unify the [log format](https://github.com/tikv/rfcs/blob/master/text/0018-unified-log-format.md) for easy collection and analysis by tools
+ Support using the Local Reader to read in the Raw Read interface [#4222](https://github.com/tikv/tikv/pull/4222)
+ Add metrics about configuration information [#4206](https://github.com/tikv/tikv/pull/4206)
+ Add metrics about key exceeding bound [#4255](https://github.com/tikv/tikv/pull/4255)
+ Add an option to control panic or return an error when encountering the key exceeding bound error [#4254](https://github.com/tikv/tikv/pull/4254)
+ Add support for the `INSERT` operation, make prewrite succeed only when keys do not exist, and eliminate `Batch Get` [#4085](https://github.com/tikv/tikv/pull/4085)
+ Use more fair batch strategy in the Batch System [#4200](https://github.com/tikv/tikv/pull/4200)
+ Support Raw scan in tikv-ctl [#3825](https://github.com/tikv/tikv/pull/3825)

## Tools

+ TiDB Binlog
    - Add the Arbiter tool that supports reading binlog from Kafka and replicate the data into MySQL
    - Support filtering files that do not need to be replicated
    - Support replicating generated columns
+ Lightning
    - Support disabling TiKV periodic Level-1 compaction, and when the TiKV cluster version is 2.1.4 or later, Level-1 compaction is automatically executed in the import mode [#119](https://github.com/pingcap/tidb-lightning/pull/119), [#4199](https://github.com/tikv/tikv/pull/4199)
    - Add the `table_concurrency` configuration item to limit the number of import engines ("16" by default) and avoid overusing the importer disk space [#119](https://github.com/pingcap/tidb-lightning/pull/119)
    - Support saving the intermediate state SST to the disk, to reduce memory usage [#4369](https://github.com/tikv/tikv/pull/4369)
    - Optimize the import performance of TiKV-Importer and support separate import of data and indexes for large tables [#132](https://github.com/pingcap/tidb-lightning/pull/132)
    - Support importing CSV files [#111](https://github.com/pingcap/tidb-lightning/pull/111)
+ Data replication comparison tool (sync-diff-inspector)
    - Support using TiDB statistics to split chunks to be compared [#197](https://github.com/pingcap/tidb-tools/pull/197)
    - Support using multiple columns to split chunks to be compared [#197](https://github.com/pingcap/tidb-tools/pull/197)