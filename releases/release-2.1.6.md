---
title: TiDB 2.1.6 Release Notes
summary: TiDB 2.1.6 and TiDB Ansible 2.1.6 were released on March 15, 2019. The release includes improvements in stability, SQL optimizer, statistics, and execution engine. Fixes and enhancements were made in SQL optimizer/executor, server, DDL, TiKV, and Tools. Notable changes include support for log_bin variable, sanity check for transactions, and fixing import failure due to non-alphanumeric characters in schema names.
---

# TiDB 2.1.6 Release Notes

On March 15, 2019, TiDB 2.1.6 is released. The corresponding TiDB Ansible 2.1.6 is also released. Compared with TiDB 2.1.5, this release has greatly improved the stability, the SQL optimizer, statistics, and the execution engine.

## TiDB

+ SQL Optimizer/Executor
    - Optimize planner to select the outer table based on cost when both tables are specified in Hint of `TIDB_INLJ` [#9615](https://github.com/pingcap/tidb/pull/9615)
    - Fix the issue that `IndexScan` cannot be selected correctly in some cases [#9587](https://github.com/pingcap/tidb/pull/9587)
    - Fix incompatibility with MySQL of check in the `agg` function in subqueries [#9551](https://github.com/pingcap/tidb/pull/9551)
    - Make `show stats_histograms` only output valid columns to avoid panics [#9502](https://github.com/pingcap/tidb/pull/9502)

+ Server
    - Support the `log_bin` variable to enable/disable Binlog [#9634](https://github.com/pingcap/tidb/pull/9634)
    - Add a sanity check for transactions to avoid false transaction commit [#9559](https://github.com/pingcap/tidb/pull/9559)
    - Fix the issue that setting variables may lead to panic  [#9539](https://github.com/pingcap/tidb/pull/9539)

+ DDL
    - Fix the issue that the `Create Table Like` statement causes panic in some cases [#9652](https://github.com/pingcap/tidb/pull/9652)
    - Enable the `AutoSync` feature of etcd clients to avoid connection issues between TiDB and etcd in some cases [#9600](https://github.com/pingcap/tidb/pull/9600)

## TiKV

- Fix the issue that a `protobuf` parsing failure would in some cases cause a `StoreNotMatch` error [#4303](https://github.com/tikv/tikv/pull/4303)

## Tools

+ Lightning
    - Change the default `region-split-size` of importer to 512 MiB [#4369](https://github.com/tikv/tikv/pull/4369)
    - Save the intermediate SST previously cached in memory to the local disk to reduce memory usage [#4369](https://github.com/tikv/tikv/pull/4369)
    - Limit the memory usage of RocksDB [#4369](https://github.com/tikv/tikv/pull/4369)
    - Fix the issue that Regions are scattered before scheduling is finished [#4369](https://github.com/tikv/tikv/pull/4369)
    - Separate importing of data and indexes for large tables to effectively reduce time consumption when importing in batches [#132](https://github.com/pingcap/tidb-lightning/pull/132)
    - Support CSV [#111](https://github.com/pingcap/tidb-lightning/pull/111)
    - Fix the error of import failure due to non-alphanumeric characters in schema names [#9547](https://github.com/pingcap/tidb/pull/9547)
