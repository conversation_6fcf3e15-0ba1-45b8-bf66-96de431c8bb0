---
title: TiDB 2.1 RC1 Release Notes
summary: TiDB 2.1 RC1 was released on August 24, 2018, with improvements in stability, SQL optimizer, statistics, and execution engine. The release includes fixes for various issues in SQL optimizer and execution engine. PD introduces version control, rolling update, and region merge features. TiKV supports batch split and row-based region splitting for improved efficiency. Overall, the release focuses on performance optimization and bug fixes.
---

# TiDB 2.1 RC1 Release Notes

On August 24, 2018, TiDB 2.1 RC1 is released! Compared with TiDB 2.1 Beta, this release has great improvement in stability, SQL optimizer, statistics information, and execution engine.

## TiDB

- SQL Optimizer
    - Fix the issue that a wrong result is returned after the correlated subquery is decorrelated in some cases [#6972](https://github.com/pingcap/tidb/pull/6972)
    - Optimize the output result of `Explain` [#7011](https://github.com/pingcap/tidb/pull/7011)[#7041](https://github.com/pingcap/tidb/pull/7041)
    - Optimize the choosing strategy of the outer table for `IndexJoin` [#7019](https://github.com/pingcap/tidb/pull/7019)
    - Remove the Plan Cache of the non-`PREPARE` statement [#7040](https://github.com/pingcap/tidb/pull/7040)
    - Fix the issue that the `INSERT` statement is not parsed and executed correctly in some cases [#7068](https://github.com/pingcap/tidb/pull/7068)
    - Fix the issue that the `IndexJoin` result is not correct in some cases [#7150](https://github.com/pingcap/tidb/pull/7150)
    - Fix the issue that the `NULL` value cannot be found using the unique index in some cases [#7163](https://github.com/pingcap/tidb/pull/7163)
    - Fix the range computing issue of the prefix index in UTF-8 [#7194](https://github.com/pingcap/tidb/pull/7194)
    - Fix the issue that result is not correct caused by eliminating the `Project` operator in some cases [#7257](https://github.com/pingcap/tidb/pull/7257)
    - Fix the issue that `USE INDEX(PRIMARY)` cannot be used when the primary key is an integer [#7316](https://github.com/pingcap/tidb/pull/7316)
    - Fix the issue that the index range cannot be computed using the correlated column in some cases [#7357](https://github.com/pingcap/tidb/pull/7357)
- SQL Execution Engine
    - Fix the issue that the daylight saving time is not computed correctly in some cases [#6823](https://github.com/pingcap/tidb/pull/6823)
    - Refactor the aggregation function framework to improve the execution efficiency of the `Stream` and `Hash` aggregation operators [#6852](https://github.com/pingcap/tidb/pull/6852)
    - Fix the issue that the `Hash` aggregation operator cannot exit normally in some cases [#6982](https://github.com/pingcap/tidb/pull/6982)
    - Fix the issue that `BIT_AND`/`BIT_OR`/`BIT_XOR` does not handle the non-integer data correctly [#6994](https://github.com/pingcap/tidb/pull/6994)
    - Optimize the execution speed of the `REPLACE INTO` statement and increase the performance nearly 10 times [#7027](https://github.com/pingcap/tidb/pull/7027)
    - Optimize the memory usage of time type data and decrease the memory usage of the time type data by fifty percent [#7043](https://github.com/pingcap/tidb/pull/7043)
    - Fix the issue that the returned result is mixed with signed and unsigned integers in the `UNION` statement is not compatible with MySQL [#7112](https://github.com/pingcap/tidb/pull/7112)
    - Fix the panic issue caused by the too much memory applied by `LPAD`/`RPAD`/`TO_BASE64`/`FROM_BASE64`/`REPEAT` [#7171](https://github.com/pingcap/tidb/pull/7171) [#7266](https://github.com/pingcap/tidb/pull/7266) [#7409](https://github.com/pingcap/tidb/pull/7409) [#7431](https://github.com/pingcap/tidb/pull/7431)
    - Fix the incorrect result when `MergeJoin`/`IndexJoin` handles the `NULL` value [#7255](https://github.com/pingcap/tidb/pull/7255)
    - Fix the incorrect result of `Outer Join` in some cases [#7288](https://github.com/pingcap/tidb/pull/7288)
    - Improve the error message of `Data Truncated` to facilitate locating the wrong data and the corresponding field in the table [#7401](https://github.com/pingcap/tidb/pull/7401)
    - Fix the incorrect result for `decimal` in some cases [#7001](https://github.com/pingcap/tidb/pull/7001) [#7113](https://github.com/pingcap/tidb/pull/7113) [#7202](https://github.com/pingcap/tidb/pull/7202) [#7208](https://github.com/pingcap/tidb/pull/7208)
    - Optimize the point select performance [#6937](https://github.com/pingcap/tidb/pull/6937)
    - Prohibit the isolation level of `Read Committed` to avoid the underlying problem [#7211](https://github.com/pingcap/tidb/pull/7211)
    - Fix the incorrect result of `LTRIM`/`RTRIM`/`TRIM` in some cases [#7291](https://github.com/pingcap/tidb/pull/7291)
    - Fix the issue that the `MaxOneRow` operator cannot guarantee that the returned result does not exceed one row [#7375](https://github.com/pingcap/tidb/pull/7375)
    - Divide the Coprocessor requests with too many ranges [#7454](https://github.com/pingcap/tidb/pull/7454)
- Statistics
    - Optimize the mechanism of statistics dynamic collection [#6796](https://github.com/pingcap/tidb/pull/6796)
    - Fix the issue that `Auto Analyze` does not work when data is updated frequently [#7022](https://github.com/pingcap/tidb/pull/7022)
    - Decrease the Write conflicts during the statistics dynamic update process [#7124](https://github.com/pingcap/tidb/pull/7124)
    - Optimize the cost estimation when the statistics is incorrect [#7175](https://github.com/pingcap/tidb/pull/7175)
    - Optimize the `AccessPath` cost estimation strategy [#7233](https://github.com/pingcap/tidb/pull/7233)
- Server
    - Fix the bug in loading privilege information [#6976](https://github.com/pingcap/tidb/pull/6976)
    - Fix the issue that the `Kill` command is too strict with privilege check [#6954](https://github.com/pingcap/tidb/pull/6954)
    - Fix the issue of removing some binary numeric types [#6922](https://github.com/pingcap/tidb/pull/6922)
    - Shorten the output log [#7029](https://github.com/pingcap/tidb/pull/7029)
    - Handle the `mismatchClusterID` issue [#7053](https://github.com/pingcap/tidb/pull/7053)
    - Add the `advertise-address` configuration item [#7078](https://github.com/pingcap/tidb/pull/7078)
    - Add the `GrpcKeepAlive` option [#7100](https://github.com/pingcap/tidb/pull/7100)
    - Add the connection or `Token` time monitor [#7110](https://github.com/pingcap/tidb/pull/7110)
    - Optimize the data decoding performance [#7149](https://github.com/pingcap/tidb/pull/7149)
    - Add the `PROCESSLIST` table in `INFORMMATION_SCHEMA` [#7236](https://github.com/pingcap/tidb/pull/7236)
    - Fix the order issue when multiple rules are hit in verifying the privilege [#7211](https://github.com/pingcap/tidb/pull/7211)
    - Change some default values of encoding related system variables to UTF-8 [#7198](https://github.com/pingcap/tidb/pull/7198)
    - Make the slow query log show more detailed information [#7302](https://github.com/pingcap/tidb/pull/7302)
    - Support registering tidb-server related information in PD and obtaining this information by HTTP API [#7082](https://github.com/pingcap/tidb/pull/7082)
- Compatibility
    - Support Session variables `warning_count` and `error_count` [#6945](https://github.com/pingcap/tidb/pull/6945)
    - Add `Scope` check when reading the system variables [#6958](https://github.com/pingcap/tidb/pull/6958)
    - Support the `MAX_EXECUTION_TIME` syntax [#7012](https://github.com/pingcap/tidb/pull/7012)
    - Support more statements of the `SET` syntax [#7020](https://github.com/pingcap/tidb/pull/7020)
    - Add validity check when setting system variables [#7117](https://github.com/pingcap/tidb/pull/7117)
    - Add the verification of the number of `PlaceHolder`s in the `Prepare` statement [#7162](https://github.com/pingcap/tidb/pull/7162)
    - Support `set character_set_results = null` [#7353](https://github.com/pingcap/tidb/pull/7353)
    - Support the `flush status` syntax [#7369](https://github.com/pingcap/tidb/pull/7369)
    - Fix the column size of `SET` and `ENUM` types in `information_schema` [#7347](https://github.com/pingcap/tidb/pull/7347)
    - Support the `NATIONAL CHARACTER` syntax of statements for creating a table [#7378](https://github.com/pingcap/tidb/pull/7378)
    - Support the `CHARACTER SET` syntax in the `LOAD DATA` statement [#7391](https://github.com/pingcap/tidb/pull/7391)
    - Fix the column information of the `SET` and `ENUM` types [#7417](https://github.com/pingcap/tidb/pull/7417)
    - Support the `IDENTIFIED WITH` syntax in the `CREATE USER` statement [#7402](https://github.com/pingcap/tidb/pull/7402)
    - Fix the precision losing issue during `TIMESTAMP` computing process [#7418](https://github.com/pingcap/tidb/pull/7418)
    - Support the validity verification of more `SYSTEM` variables [#7196](https://github.com/pingcap/tidb/pull/7196)
    - Fix the incorrect result when the `CHAR_LENGTH` function computes the binary string [#7410](https://github.com/pingcap/tidb/pull/7410)
    - Fix the incorrect `CONCAT` result in a statement involving `GROUP BY` [#7448](https://github.com/pingcap/tidb/pull/7448)
    - Fix the imprecise type length issue when casting the `DECIMAL` type to the `STRING` type [#7451](https://github.com/pingcap/tidb/pull/7451)
- DML
    - Fix the stability issue of the `Load Data` statement [#6927](https://github.com/pingcap/tidb/pull/6927)
    - Fix the memory usage issue when performing some `Batch` operations [#7086](https://github.com/pingcap/tidb/pull/7086)
    - Improve the performance of the `Replace Into` statement [#7027](https://github.com/pingcap/tidb/pull/7027)
    - Fix the inconsistent precision issue when writing `CURRENT_TIMESTAMP` [#7355](https://github.com/pingcap/tidb/pull/7355)
- DDL
    - Improve the method of DDL judging whether `Schema` is replicated to avoid misjudgement in some cases [#7319](https://github.com/pingcap/tidb/pull/7319)
    - Fix the `SHOW CREATE TABLE` result in adding index process [#6993](https://github.com/pingcap/tidb/pull/6993)
    - Allow the default value of `text`/`blob`/`json` to be NULL in non-restrict `sql-mode` [#7230](https://github.com/pingcap/tidb/pull/7230)
    - Fix the `ADD INDEX` issue in some cases [#7142](https://github.com/pingcap/tidb/pull/7142)
    - Increase the speed of adding `UNIQUE-KEY` index operation largely [#7132](https://github.com/pingcap/tidb/pull/7132)
    - Fix the truncating issue of the prefix index in UTF-8 character set [#7109](https://github.com/pingcap/tidb/pull/7109)
    - Add the environment variable `tidb_ddl_reorg_priority` to control the priority of the `add-index` operation [#7116](https://github.com/pingcap/tidb/pull/7116)
    - Fix the display issue of `AUTO-INCREMENT` in `information_schema.tables` [#7037](https://github.com/pingcap/tidb/pull/7037)
    - Support the `admin show ddl jobs <number>` command and support output specified number of DDL jobs [#7028](https://github.com/pingcap/tidb/pull/7028)
    - Support parallel DDL job execution [#6955](https://github.com/pingcap/tidb/pull/6955)
- [Table Partition](https://github.com/pingcap/tidb/projects/6) (Experimental)
    - Support top level partition
    - Support `Range Partition`

## PD

- Features
    - Introduce the version control mechanism and support rolling update of the cluster with compatibility
    - Enable the `region merge` feature
    - Support the `GetPrevRegion` interface
    - Support splitting Regions in batch
    - Support storing the GC safepoint
- Improvements
    - Optimize the issue that TSO allocation is affected by the system clock going backwards
    - Optimize the performance of handling Region heartbeats
    - Optimize the Region tree performance
    - Optimize the performance of computing hotspot statistics
    - Optimize returning the error code of API interface
    - Add options of controlling scheduling strategies
    - Prohibit using special characters in `label`
    - Improve the scheduling simulator
    - Support splitting Regions using statistics in pd-ctl
    - Support formatting JSON output by calling `jq` in pd-ctl
    - Add metrics about etcd Raft state machine
- Bug fixes
    - Fix the issue that the namespace is not reloaded after switching Leader
    - Fix the issue that namespace scheduling exceeds the schedule limit
    - Fix the issue that hotspot scheduling exceeds the schedule limit
    - Fix the issue that wrong logs are output when the PD client closes
    - Fix the wrong statistics of Region heartbeat latency

## TiKV

- Features
    - Support `batch split` to avoid too large Regions caused by the Write operation on hot Regions
    - Support splitting Regions based on the number of rows to improve the index scan efficiency
- Performance
    - Use `LocalReader` to separate the Read operation from the raftstore thread to lower the Read latency
    - Refactor the MVCC framework, optimize the memory usage and improve the scan Read performance
    - Support splitting Regions based on statistics estimation to reduce the I/O usage
    - Optimize the issue that the Read performance is affected by continuous Write operations on the rollback record
    - Reduce the memory usage of pushdown aggregation computing
- Improvements
    - Add the pushdown support for a large number of built-in functions and better charset support
    - Optimize the GC workflow, improve the GC speed and decrease the impact of GC on the system
    - Enable `prevote` to speed up service recovery when the network is abnormal
    - Add the related configuration items of RocksDB log files
    - Adjust the default configuration of `scheduler_latch`
    - Support setting whether to compact the data in the bottom layer of RocksDB when using tikv-ctl to compact data manually
    - Add the check for environment variables when starting TiKV
    - Support dynamically configuring the `dynamic_level_bytes` parameter based on the existing data
    - Support customizing the log format
    - Integrate tikv-fail in tikv-ctl
    - Add I/O metrics of threads
- Bug fixes
    - Fix decimal related issues
    - Fix the issue that `gRPC max_send_message_len` is set mistakenly
    - Fix the issue caused by misconfiguration of `region_size`
