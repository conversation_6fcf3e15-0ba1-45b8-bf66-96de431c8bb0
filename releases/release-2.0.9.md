---
title: TiDB 2.0.9 Release Notes
summary: TiDB 2.0.9 was released on November 19, 2018, with significant improvements in system compatibility and stability. The release includes fixes for various issues, such as empty statistics histogram, panic issue with UNION ALL statement, stack overflow issue, and support for specifying utf8mb4 character set. PD and TiKV also received fixes for issues related to server startup failure and interface limits.
---

# TiDB 2.0.9 Release Notes

On November 19, 2018, TiDB 2.0.9 is released. Compared with TiDB 2.0.8, this release has great improvement in system compatibility and stability.

## TiDB

- Fix the issue caused by the empty statistics histogram [#7927](https://github.com/pingcap/tidb/pull/7927)
- Fix the panic issue of the `UNION ALL` statement in some cases [#7942](https://github.com/pingcap/tidb/pull/7942)
- Fix the stack overflow issue caused by wrong DDL Jobs [#7959](https://github.com/pingcap/tidb/pull/7959)
- Add the slow log for the `Commit` operation [#7983](https://github.com/pingcap/tidb/pull/7983)
- Fix the panic issue caused by the too large `Limit` value [#8004](https://github.com/pingcap/tidb/pull/8004)
- Support specifying the `utf8mb4` character set in the `USING` clause [#8048](https://github.com/pingcap/tidb/pull/8048)
- Make the `TRUNCATE` built-in function support parameters of unsigned integer type [#8069](https://github.com/pingcap/tidb/pull/8069)
- Fix the selectivity estimation issue of the primary key for the statistics module in some cases [#8150](https://github.com/pingcap/tidb/pull/8150)
- Add the `Session` variable to control whether `_tidb_rowid` is allowed to be written in [#8126](https://github.com/pingcap/tidb/pull/8126)
- Fix the panic issue of `PhysicalProjection` in some cases [#8154](https://github.com/pingcap/tidb/pull/8154)
- Fix the unstable results of the `Union` statement in some cases [#8168](https://github.com/pingcap/tidb/pull/8168)
- Fix the issue that `NULL` is not returned by `values` in the non-`Insert` statement [#8179](https://github.com/pingcap/tidb/pull/8179)
- Fix the issue that the statistics module cannot clear the outdated data in some cases [#8184](https://github.com/pingcap/tidb/pull/8184)
- Make the maximum allowed running time for a transaction a configurable option [#8209](https://github.com/pingcap/tidb/pull/8209)
- Fix the wrong comparison algorithm of `expression rewriter` in some cases [#8288](https://github.com/pingcap/tidb/pull/8288)
- Eliminate the extra columns generated by the `UNION ORDER BY` statement [#8307](https://github.com/pingcap/tidb/pull/8307)
- Support the `admin show next_row_id` statement [#8274](https://github.com/pingcap/tidb/pull/8274)
- Fix the escape issue of special characters in the `Show Create Table` statement [#8321](https://github.com/pingcap/tidb/pull/8321)
- Fix the unexpected errors in the `UNION` statement in some cases [#8318](https://github.com/pingcap/tidb/pull/8318)
- Fix the issue that canceling a DDL job causes no rollback of a schema in some cases [#8312](https://github.com/pingcap/tidb/pull/8312)
- Change `tidb_max_chunk_size` to a global variable [#8333](https://github.com/pingcap/tidb/pull/8333)
- Add an upper bound to the `Scan` command of ticlient, to avoid overbound scan [#8309](https://github.com/pingcap/tidb/pull/8309) [#8310](https://github.com/pingcap/tidb/pull/8310)

## PD

- Fix the issue that the PD server gets stuck caused by etcd startup failure [#1267](https://github.com/pingcap/pd/pull/1267)
- Fix the issues related to `pd-ctl` reading the Region key [#1298](https://github.com/pingcap/pd/pull/1298) [#1299](https://github.com/pingcap/pd/pull/1299) [#1308](https://github.com/pingcap/pd/pull/1308)
- Fix the issue that the `regions/check` API returns the wrong result [#1311](https://github.com/pingcap/pd/pull/1311)
- Fix the issue that PD cannot restart join after a PD join failure [#1279](https://github.com/pingcap/pd/pull/1279)

## TiKV

- Add the `end-key` limit to the `kv_scan` interface [#3749](https://github.com/tikv/tikv/pull/3749)
- Abandon the `max-tasks-xxx` configuration and add `max-tasks-per-worker-xxx` [#3093](https://github.com/tikv/tikv/pull/3093)
- Fix the `CompactFiles` issue in RocksDB [#3789](https://github.com/tikv/tikv/pull/3789)