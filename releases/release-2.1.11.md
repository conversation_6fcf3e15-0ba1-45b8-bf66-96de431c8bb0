---
title: TiDB 2.1.11 Release Notes
summary: TiDB 2.1.11 was released on June 03, 2019. It includes fixes for various issues in TiDB, PD, TiKV, and Tools. Some highlights are the fix for incorrect schema in delete from join, calculation errors of unix_timestamp(), and the addition of Drainer parameters in TiDB Ansible.
---

# TiDB 2.1.11 Release Notes

Release date: June 03, 2019

TiDB version: 2.1.11

TiDB Ansible version: 2.1.11

## TiDB

- Fix the issue that incorrect schema is used for `delete from join` [#10595](https://github.com/pingcap/tidb/pull/10595)
- Fix the issue that the built-in `CONVERT()` may return incorrect field type [#10263](https://github.com/pingcap/tidb/pull/10263)
- Merge non-overlapped feedback when updating bucket count [#10569](https://github.com/pingcap/tidb/pull/10569)
- Fix calculation errors of `unix_timestamp()-unix_timestamp(now())` [#10491](https://github.com/pingcap/tidb/pull/10491)
- Fix the incompatibility issue of `period_diff` with MySQL 8.0 [#10501](https://github.com/pingcap/tidb/pull/10501)
- Skip `Virtual Column` when collecting statistics to avoid exceptions [#10628](https://github.com/pingcap/tidb/pull/10628)
- Support the `SHOW OPEN TABLES` statement [#10374](https://github.com/pingcap/tidb/pull/10374)
- Fix the issue that goroutine leak may happen in some cases [#10656](https://github.com/pingcap/tidb/pull/10656)
- Fix the issue that setting the `tidb_snapshot` variable in some cases may cause incorrect parsing of time format  [#10637](https://github.com/pingcap/tidb/pull/10637)

## PD

- Fix the issue that hots Region may fail to be scheduled due to `balance-region` [#1551](https://github.com/pingcap/pd/pull/1551)
- Set hotspot related scheduling priorities to high [#1551](https://github.com/pingcap/pd/pull/1551)
- Add two configuration items [#1551](https://github.com/pingcap/pd/pull/1551)
    - `hot-region-schedule-limit` to control the maximum number of concurrent hotspot scheduling tasks
    - `hot-region-cache-hits-threshold` to identify a hot Region

## TiKV

- Fix the issue that the learner reads an empty index when there is only one leader and one learner [#4751](https://github.com/tikv/tikv/pull/4751)
- Process `ScanLock` and `ResolveLock` in the thread pool with a high priority to reduce their impacts on commands with a normal priority [#4791](https://github.com/tikv/tikv/pull/4791)
- Sync all files of received snapshots [#4811](https://github.com/tikv/tikv/pull/4811)

## Tools

- TiDB Binlog
    - Limit data deletion speed during GC to avoid QPS degrading caused by `WritePause` [#620](https://github.com/pingcap/tidb-binlog/pull/620)

## TiDB Ansible

- Add Drainer parameters [#760](https://github.com/pingcap/tidb-ansible/pull/760)
