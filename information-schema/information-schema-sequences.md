---
title: SEQUENCES
summary: Learn the `SEQUENCES` INFORMATION_SCHEMA table.
---

# SEQUENCES

The `SEQUENCES` table provides information about sequences. The [sequences feature](/sql-statements/sql-statement-create-sequence.md) is modeled on a similar feature in MariaDB.

```sql
USE INFORMATION_SCHEMA;
DESC SEQUENCES;
```

The output is as follows:

```sql
+-----------------+--------------+------+------+---------+-------+
| Field           | Type         | Null | Key  | Default | Extra |
+-----------------+--------------+------+------+---------+-------+
| TABLE_CATALOG   | varchar(512) | NO   |      | NULL    |       |
| SEQUENCE_SCHEMA | varchar(64)  | NO   |      | NULL    |       |
| SEQUENCE_NAME   | varchar(64)  | NO   |      | NULL    |       |
| CACHE           | tinyint(0)   | NO   |      | NULL    |       |
| CACHE_VALUE     | bigint(21)   | YES  |      | NULL    |       |
| CYCLE           | tinyint(0)   | NO   |      | NULL    |       |
| INCREMENT       | bigint(21)   | NO   |      | NULL    |       |
| MAX_VALUE       | bigint(21)   | YES  |      | NULL    |       |
| MIN_VALUE       | bigint(21)   | YES  |      | NULL    |       |
| START           | bigint(21)   | YES  |      | NULL    |       |
| COMMENT         | varchar(64)  | YES  |      | NULL    |       |
+-----------------+--------------+------+------+---------+-------+
11 rows in set (0.00 sec)
```

Create a sequence `test.seq` and query the next value of the sequence:

```sql
CREATE SEQUENCE test.seq;
SELECT NEXTVAL(test.seq);
SELECT * FROM sequences\G
```

The output is as follows:

```sql
+-------------------+
| NEXTVAL(test.seq) |
+-------------------+
|                 1 |
+-------------------+
1 <USER> <GROUP> set (0.01 sec)
```

View all sequences:

```sql
SELECT * FROM SEQUENCES\G
```

The output is as follows:

```sql
*************************** 1. row ***************************
  TABLE_CATALOG: def
SEQUENCE_SCHEMA: test
  SEQUENCE_NAME: seq
          CACHE: 1
    CACHE_VALUE: 1000
          CYCLE: 0
      INCREMENT: 1
      MAX_VALUE: 9223372036854775806
      MIN_VALUE: 1
          START: 1
        COMMENT:
1 row in set (0.00 sec)
```

## See also

- [`CREATE SEQUENCE`](/sql-statements/sql-statement-create-sequence.md)
- [`SHOW CREATE SEQUENCE`](/sql-statements/sql-statement-show-create-sequence.md)
- [`ALTER SEQUENCE`](/sql-statements/sql-statement-alter-sequence.md)
- [`DROP SEQUENCE`](/sql-statements/sql-statement-drop-sequence.md)
- [Sequence functions](/functions-and-operators/sequence-functions.md)