<!-- markdownlint-disable MD007 -->
<!-- markdownlint-disable MD041 -->

# Table of Contents

## GET STARTED

- Why TiDB Cloud
  - [TiDB Cloud Introduction](/tidb-cloud/tidb-cloud-intro.md)
  - [MySQL Compatibility](/mysql-compatibility.md)
- Get Started with TiDB Cloud
  - [Try Out {{{ .starter }}}](/tidb-cloud/tidb-cloud-quickstart.md)
  - [Try Out TiDB + AI](/vector-search/vector-search-get-started-using-python.md)
  - [Try Out HTAP](/tidb-cloud/tidb-cloud-htap-quickstart.md)
  - [Try Out TiDB Cloud CLI](/tidb-cloud/get-started-with-cli.md)
  - [Perform a PoC](/tidb-cloud/tidb-cloud-poc.md)
- Key Concepts
  - [Overview](/tidb-cloud/key-concepts.md)
  - [Architecture](/tidb-cloud/architecture-concepts.md)
  - [Database Schema](/tidb-cloud/database-schema-concepts.md)
  - [Transactions](/tidb-cloud/transaction-concepts.md)
  - [SQL](/tidb-cloud/sql-concepts.md)
  - [AI Features](/tidb-cloud/ai-feature-concepts.md)
  - [Data Service](/tidb-cloud/data-service-concepts.md) ![BETA](/media/tidb-cloud/blank_transparent_placeholder.png)
  - [Scalability](/tidb-cloud/scalability-concepts.md)
  - High Availability
    - [High Availability in {{{ .starter }}}](/tidb-cloud/serverless-high-availability.md)
    - [High Availability in TiDB Cloud Dedicated](/tidb-cloud/high-availability-with-multi-az.md)
  - [Monitoring](/tidb-cloud/monitoring-concepts.md)
  - [Data Streaming](/tidb-cloud/data-streaming-concepts.md)
  - [Backup & Restore](/tidb-cloud/backup-and-restore-concepts.md)
  - [Security](/tidb-cloud/security-concepts.md)

## DEVELOP

- Development Quick Start
  - [Developer Guide Overview](/develop/dev-guide-overview.md)
  - [Build a {{{ .starter }}} Cluster](/develop/dev-guide-build-cluster-in-cloud.md)
  - [CRUD SQL in TiDB](/develop/dev-guide-tidb-crud-sql.md)
- Connect to TiDB Cloud
  - GUI Database Tools
    - [JetBrains DataGrip](/develop/dev-guide-gui-datagrip.md)
    - [DBeaver](/develop/dev-guide-gui-dbeaver.md)
    - [VS Code](/develop/dev-guide-gui-vscode-sqltools.md)
    - [MySQL Workbench](/develop/dev-guide-gui-mysql-workbench.md)
    - [Navicat](/develop/dev-guide-gui-navicat.md)
  - [Choose Driver or ORM](/develop/dev-guide-choose-driver-or-orm.md)
  - BI
    - [Looker Studio](/tidb-cloud/dev-guide-bi-looker-studio.md)
  - Java
    - [JDBC](/develop/dev-guide-sample-application-java-jdbc.md)
    - [MyBatis](/develop/dev-guide-sample-application-java-mybatis.md)
    - [Hibernate](/develop/dev-guide-sample-application-java-hibernate.md)
    - [Spring Boot](/develop/dev-guide-sample-application-java-spring-boot.md)
    - [Connection Pools and Connection Parameters](/develop/dev-guide-connection-parameters.md)
  - Go
    - [Go-MySQL-Driver](/develop/dev-guide-sample-application-golang-sql-driver.md)
    - [GORM](/develop/dev-guide-sample-application-golang-gorm.md)
  - Python
    - [mysqlclient](/develop/dev-guide-sample-application-python-mysqlclient.md)
    - [MySQL Connector/Python](/develop/dev-guide-sample-application-python-mysql-connector.md)
    - [PyMySQL](/develop/dev-guide-sample-application-python-pymysql.md)
    - [SQLAlchemy](/develop/dev-guide-sample-application-python-sqlalchemy.md)
    - [peewee](/develop/dev-guide-sample-application-python-peewee.md)
    - [Django](/develop/dev-guide-sample-application-python-django.md)
  - Node.js
    - [node-mysql2](/develop/dev-guide-sample-application-nodejs-mysql2.md)
    - [mysql.js](/develop/dev-guide-sample-application-nodejs-mysqljs.md)
    - [Prisma](/develop/dev-guide-sample-application-nodejs-prisma.md)
    - [Sequelize](/develop/dev-guide-sample-application-nodejs-sequelize.md)
    - [TypeORM](/develop/dev-guide-sample-application-nodejs-typeorm.md)
    - [Next.js](/develop/dev-guide-sample-application-nextjs.md)
    - [AWS Lambda](/develop/dev-guide-sample-application-aws-lambda.md)
  - Ruby
    - [mysql2](/develop/dev-guide-sample-application-ruby-mysql2.md)
    - [Rails](/develop/dev-guide-sample-application-ruby-rails.md)
  - C#
    - [C#](/develop/dev-guide-sample-application-cs.md)
  - [WordPress](/tidb-cloud/dev-guide-wordpress.md)
  - Serverless Driver ![BETA](/media/tidb-cloud/blank_transparent_placeholder.png)
    - [{{{ .starter }}} Driver](/tidb-cloud/serverless-driver.md)
    - [Node.js Example](/tidb-cloud/serverless-driver-node-example.md)
    - [Prisma Example](/tidb-cloud/serverless-driver-prisma-example.md)
    - [Kysely Example](/tidb-cloud/serverless-driver-kysely-example.md)
    - [Drizzle Example](/tidb-cloud/serverless-driver-drizzle-example.md)
- Development Reference
  - Design Database Schema
    - [Overview](/develop/dev-guide-schema-design-overview.md)
    - [Create a Database](/develop/dev-guide-create-database.md)
    - [Create a Table](/develop/dev-guide-create-table.md)
    - [Create a Secondary Index](/develop/dev-guide-create-secondary-indexes.md)
  - Write Data
    - [Insert Data](/develop/dev-guide-insert-data.md)
    - [Update Data](/develop/dev-guide-update-data.md)
    - [Delete Data](/develop/dev-guide-delete-data.md)
    - [Periodically Delete Expired Data Using TTL (Time to Live)](/time-to-live.md)
    - [Prepared Statements](/develop/dev-guide-prepared-statement.md)
  - Read Data
    - [Query Data from a Single Table](/develop/dev-guide-get-data-from-single-table.md)
    - [Multi-table Join Queries](/develop/dev-guide-join-tables.md)
    - [Subquery](/develop/dev-guide-use-subqueries.md)
    - [Paginate Results](/develop/dev-guide-paginate-results.md)
    - [Views](/develop/dev-guide-use-views.md)
    - [Temporary Tables](/develop/dev-guide-use-temporary-tables.md)
    - [Common Table Expression](/develop/dev-guide-use-common-table-expression.md)
    - Read Replica Data
      - [Follower Read](/develop/dev-guide-use-follower-read.md)
      - [Stale Read](/develop/dev-guide-use-stale-read.md)
    - [HTAP Queries](/develop/dev-guide-hybrid-oltp-and-olap-queries.md)
  - Transaction
    - [Overview](/develop/dev-guide-transaction-overview.md)
    - [Optimistic and Pessimistic Transactions](/develop/dev-guide-optimistic-and-pessimistic-transaction.md)
    - [Transaction Restraints](/develop/dev-guide-transaction-restraints.md)
    - [Handle Transaction Errors](/develop/dev-guide-transaction-troubleshoot.md)
  - Optimize
    - [Overview](/develop/dev-guide-optimize-sql-overview.md)
    - [SQL Performance Tuning](/develop/dev-guide-optimize-sql.md)
    - [Best Practices for Performance Tuning](/develop/dev-guide-optimize-sql-best-practices.md)
    - [Best Practices for Indexing](/develop/dev-guide-index-best-practice.md)
    - Other Optimization Methods
      - [Avoid Implicit Type Conversions](/develop/dev-guide-implicit-type-conversion.md)
      - [Unique Serial Number Generation](/develop/dev-guide-unique-serial-number-generation.md)
  - Troubleshoot
    - [SQL or Transaction Issues](/develop/dev-guide-troubleshoot-overview.md)
    - [Unstable Result Set](/develop/dev-guide-unstable-result-set.md)
    - [Timeouts](/develop/dev-guide-timeouts-in-tidb.md)
  - Development Guidelines
    - [Object Naming Convention](/develop/dev-guide-object-naming-guidelines.md)
    - [SQL Development Specifications](/develop/dev-guide-sql-development-specification.md)
  - [Bookshop Example Application](/develop/dev-guide-bookshop-schema-design.md)
  - Third-Party Support
    - [Third-Party Tools Supported by TiDB](/develop/dev-guide-third-party-support.md)
    - [Known Incompatibility Issues with Third-Party Tools](/develop/dev-guide-third-party-tools-compatibility.md)

## GUIDES

- Manage Cluster
  - Plan Your Cluster
    - [Select Your Cluster Tier](/tidb-cloud/select-cluster-tier.md)
    - [Determine Your TiDB Size](/tidb-cloud/size-your-cluster.md)
    - [TiDB Cloud Performance Reference](/tidb-cloud/tidb-cloud-performance-reference.md)
  - Manage {{{ .starter }}} Clusters
    - [Create a {{{ .starter }}} Cluster](/tidb-cloud/create-tidb-cluster-serverless.md)
    - Connect to Your {{{ .starter }}} Cluster
      - [Connection Overview](/tidb-cloud/connect-to-tidb-cluster-serverless.md)
      - [Connect via Public Endpoint](/tidb-cloud/connect-via-standard-connection-serverless.md)
      - [Connect via Private Endpoint](/tidb-cloud/set-up-private-endpoint-connections-serverless.md)
    - Branch ![BETA](/media/tidb-cloud/blank_transparent_placeholder.png)
      - [Overview](/tidb-cloud/branch-overview.md)
      - [Manage Branches](/tidb-cloud/branch-manage.md)
      - [GitHub Integration](/tidb-cloud/branch-github-integration.md)
    - [Manage Spending Limit](/tidb-cloud/manage-serverless-spend-limit.md)
    - [Back Up and Restore {{{ .starter }}} Data](/tidb-cloud/backup-and-restore-serverless.md)
    - [Export Data from {{{ .starter }}}](/tidb-cloud/serverless-export.md)
  - Manage TiDB Cloud Dedicated Clusters
    - [Create a TiDB Cloud Dedicated Cluster](/tidb-cloud/create-tidb-cluster.md)
    - Connect to Your TiDB Cloud Dedicated Cluster
      - [Connection Method Overview](/tidb-cloud/connect-to-tidb-cluster.md)
      - [Connect via Public Connection](/tidb-cloud/connect-via-standard-connection.md)
      - [Connect via Private Endpoint with AWS](/tidb-cloud/set-up-private-endpoint-connections.md)
      - [Connect via Private Endpoint with Azure](/tidb-cloud/set-up-private-endpoint-connections-on-azure.md)
      - [Connect via Private Endpoint with Google Cloud](/tidb-cloud/set-up-private-endpoint-connections-on-google-cloud.md)
      - [Connect via VPC Peering](/tidb-cloud/set-up-vpc-peering-connections.md)
      - [Connect via SQL Shell](/tidb-cloud/connect-via-sql-shell.md)
    - [Scale a TiDB Cloud Dedicated Cluster](/tidb-cloud/scale-tidb-cluster.md)
    - [Back Up and Restore TiDB Cloud Dedicated Data](/tidb-cloud/backup-and-restore.md)
    - [Pause or Resume a TiDB Cloud Dedicated Cluster](/tidb-cloud/pause-or-resume-tidb-cluster.md)
    - [Configure Maintenance Window](/tidb-cloud/configure-maintenance-window.md)
  - Use an HTAP Cluster with TiFlash
    - [TiFlash Overview](/tiflash/tiflash-overview.md)
    - [Create TiFlash Replicas](/tiflash/create-tiflash-replicas.md)
    - [Read Data from TiFlash](/tiflash/use-tidb-to-read-tiflash.md)
    - [Use MPP Mode](/tiflash/use-tiflash-mpp-mode.md)
    - [Use FastScan](/tiflash/use-fastscan.md)
    - [Supported Push-down Calculations](/tiflash/tiflash-supported-pushdown-calculations.md)
    - [TiFlash Query Result Materialization](/tiflash/tiflash-results-materialization.md)
    - [TiFlash Late Materialization](/tiflash/tiflash-late-materialization.md)
    - [Compatibility](/tiflash/tiflash-compatibility.md)
    - [Pipeline Execution Model](/tiflash/tiflash-pipeline-model.md)
  - Monitor and Alert
    - [Overview](/tidb-cloud/monitor-tidb-cluster.md)
    - [Built-in Metrics](/tidb-cloud/built-in-monitoring.md)
    - [Built-in Alerting](/tidb-cloud/monitor-built-in-alerting.md)
    - Subscribe to Alert Notifications
      - [Subscribe via Email](/tidb-cloud/monitor-alert-email.md)
      - [Subscribe via Slack](/tidb-cloud/monitor-alert-slack.md)
      - [Subscribe via Zoom](/tidb-cloud/monitor-alert-zoom.md)
    - [Cluster Events](/tidb-cloud/tidb-cloud-events.md)
    - [Third-Party Metrics Integrations](/tidb-cloud/third-party-monitoring-integrations.md) ![BETA](/media/tidb-cloud/blank_transparent_placeholder.png)
    - [TiDB Cloud Clinic](/tidb-cloud/tidb-cloud-clinic.md)
  - Tune Performance
    - [Overview](/tidb-cloud/tidb-cloud-tune-performance-overview.md)
    - Analyze Performance
      - [Use the Diagnosis Tab](/tidb-cloud/tune-performance.md)
      - [Use Index Insight](/tidb-cloud/index-insight.md) ![BETA](/media/tidb-cloud/blank_transparent_placeholder.png)
      - [Use Statement Summary Tables](/statement-summary-tables.md)
    - SQL Tuning
      - [Overview](/tidb-cloud/tidb-cloud-sql-tuning-overview.md)
      - Understanding the Query Execution Plan
        - [Overview](/explain-overview.md)
        - [`EXPLAIN` Walkthrough](/explain-walkthrough.md)
        - [Indexes](/explain-indexes.md)
        - [Joins](/explain-joins.md)
        - [MPP Queries](/explain-mpp.md)
        - [Subqueries](/explain-subqueries.md)
        - [Aggregation](/explain-aggregation.md)
        - [Views](/explain-views.md)
        - [Partitions](/explain-partitions.md)
        - [Index Merge](/explain-index-merge.md)
      - SQL Optimization Process
        - [Overview](/sql-optimization-concepts.md)
        - Logic Optimization
          - [Overview](/sql-logical-optimization.md)
          - [Subquery Related Optimizations](/subquery-optimization.md)
          - [Column Pruning](/column-pruning.md)
          - [Decorrelation of Correlated Subquery](/correlated-subquery-optimization.md)
          - [Eliminate Max/Min](/max-min-eliminate.md)
          - [Predicates Push Down](/predicate-push-down.md)
          - [Partition Pruning](/partition-pruning.md)
          - [TopN and Limit Push Down](/topn-limit-push-down.md)
          - [Join Reorder](/join-reorder.md)
          - [Derive TopN or Limit from Window Functions](/derive-topn-from-window.md)
        - Physical Optimization
          - [Overview](/sql-physical-optimization.md)
          - [Index Selection](/choose-index.md)
          - [Statistics](/statistics.md)
          - [Extended Statistics](/extended-statistics.md)
          - [Wrong Index Solution](/wrong-index-solution.md)
          - [Distinct Optimization](/agg-distinct-optimization.md)
          - [Cost Model](/cost-model.md)
          - [Runtime Filter](/runtime-filter.md)
        - [Prepared Execution Plan Cache](/sql-prepared-plan-cache.md)
        - [Non-Prepared Execution Plan Cache](/sql-non-prepared-plan-cache.md)
      - Control Execution Plans
        - [Overview](/control-execution-plan.md)
        - [Optimizer Hints](/optimizer-hints.md)
        - [SQL Plan Management](/sql-plan-management.md)
        - [The Blocklist of Optimization Rules and Expression Pushdown](/blocklist-control-plan.md)
        - [Optimizer Fix Controls](/optimizer-fix-controls.md)
      - [Index Advisor](/index-advisor.md)
    - [TiKV Follower Read](/follower-read.md)
    - [Coprocessor Cache](/coprocessor-cache.md)
    - Garbage Collection (GC)
      - [Overview](/garbage-collection-overview.md)
      - [Configuration](/garbage-collection-configuration.md)
    - [Tune TiFlash Performance](/tiflash/tune-tiflash-performance.md)
  - Optimize Resource Allocation
    - Resource Manager
      - [Use Resource Control to Achieve Resource Group Limitation and Flow Control](/tidb-resource-control-ru-groups.md)
      - [Manage Runaway Queries](/tidb-resource-control-runaway-queries.md)
      - [Manage Background Tasks](/tidb-resource-control-background-tasks.md)
    - TiDB Node Group
      - [Overview of TiDB Node Group](/tidb-cloud/tidb-node-group-overview.md)
      - [Manage TiDB Node Groups](/tidb-cloud/tidb-node-group-management.md)
  - [Upgrade a TiDB Cluster](/tidb-cloud/upgrade-tidb-cluster.md)
  - [Delete a TiDB Cluster](/tidb-cloud/delete-tidb-cluster.md)
- Migrate or Import Data
  - [Overview](/tidb-cloud/tidb-cloud-migration-overview.md)
  - Migrate Data into TiDB Cloud
    - [Migrate Existing and Incremental Data Using Data Migration](/tidb-cloud/migrate-from-mysql-using-data-migration.md)
    - [Migrate Incremental Data Using Data Migration](/tidb-cloud/migrate-incremental-data-from-mysql-using-data-migration.md)
    - [Migrate and Merge MySQL Shards of Large Datasets](/tidb-cloud/migrate-sql-shards.md)
    - [Migrate from TiDB Self-Managed to TiDB Cloud](/tidb-cloud/migrate-from-op-tidb.md)
    - [Migrate from MySQL-Compatible Databases Using AWS DMS](/tidb-cloud/migrate-from-mysql-using-aws-dms.md)
    - [Migrate from Amazon RDS for Oracle Using AWS DMS](/tidb-cloud/migrate-from-oracle-using-aws-dms.md)
  - Import Data into TiDB Cloud Dedicated
    - [Import Sample Data](/tidb-cloud/import-sample-data.md)
    - [Import CSV Files from Cloud Storage](/tidb-cloud/import-csv-files.md)
    - [Import Parquet Files from Cloud Storage](/tidb-cloud/import-parquet-files.md)
    - [Import with MySQL CLI](/tidb-cloud/import-with-mysql-cli.md)
  - Import Data into {{{ .starter }}}
    - [Import Sample Data](/tidb-cloud/import-sample-data-serverless.md)
    - [Import Local Files](/tidb-cloud/tidb-cloud-import-local-files.md)
    - [Import CSV Files from Cloud Storage](/tidb-cloud/import-csv-files-serverless.md)
    - [Import Parquet Files from Cloud Storage](/tidb-cloud/import-parquet-files-serverless.md)
    - [Import with MySQL CLI](/tidb-cloud/import-with-mysql-cli-serverless.md)
  - Reference
    - [Configure External Storage Access for TiDB Cloud Dedicated](/tidb-cloud/dedicated-external-storage.md)
    - [Configure External Storage Access for {{{ .starter }}}](/tidb-cloud/serverless-external-storage.md)
    - [Naming Conventions for Data Import](/tidb-cloud/naming-conventions-for-data-import.md)
    - [CSV Configurations for Importing Data](/tidb-cloud/csv-config-for-import-data.md)
    - [Troubleshoot Access Denied Errors during Data Import from Amazon S3](/tidb-cloud/troubleshoot-import-access-denied-error.md)
    - [Precheck Errors, Migration Errors, and Alerts for Data Migration](/tidb-cloud/tidb-cloud-dm-precheck-and-troubleshooting.md)
    - [Connect AWS DMS to TiDB Cloud clusters](/tidb-cloud/tidb-cloud-connect-aws-dms.md)
- Explore Data
  - [Chat2Query in SQL Editor](/tidb-cloud/explore-data-with-chat2query.md) ![BETA](/media/tidb-cloud/blank_transparent_placeholder.png)
  - [SQL Proxy Account](/tidb-cloud/sql-proxy-account.md)
- Vector Search ![BETA](/media/tidb-cloud/blank_transparent_placeholder.png)
  - [Overview](/vector-search/vector-search-overview.md)
  - Get Started
    - [Get Started with SQL](/vector-search/vector-search-get-started-using-sql.md)
    - [Get Started with Python](/vector-search/vector-search-get-started-using-python.md)
  - Auto Embedding
    - [Overview](/tidb-cloud/vector-search-auto-embedding-overview.md)
    - [Amazon Titan Embeddings](/tidb-cloud/vector-search-auto-embedding-amazon-titan.md)
    - [Cohere Embeddings](/tidb-cloud/vector-search-auto-embedding-cohere.md)
    - [Jina AI Embeddings](/tidb-cloud/vector-search-auto-embedding-jina-ai.md)
    - [OpenAI Embeddings](/tidb-cloud/vector-search-auto-embedding-openai.md)
    - [Gemini Embeddings](/tidb-cloud/vector-search-auto-embedding-gemini.md)
    - [HuggingFace Embeddings](/tidb-cloud/vector-search-auto-embedding-huggingface.md)
    - [NVIDIA NIM Embeddings](/tidb-cloud/vector-search-auto-embedding-nvidia-nim.md)
  - Integrations
    - [Overview](/vector-search/vector-search-integration-overview.md)
    - AI Frameworks
      - [LlamaIndex](/vector-search/vector-search-integrate-with-llamaindex.md)
      - [Langchain](/vector-search/vector-search-integrate-with-langchain.md)
    - AI Services
      - [Amazon Bedrock](/tidb-cloud/vector-search-integrate-with-amazon-bedrock.md)
    - Embedding Models/Services
      - [Jina AI](/vector-search/vector-search-integrate-with-jinaai-embedding.md)
    - ORM Libraries
      - [SQLAlchemy](/vector-search/vector-search-integrate-with-sqlalchemy.md)
      - [peewee](/vector-search/vector-search-integrate-with-peewee.md)
      - [Django ORM](/vector-search/vector-search-integrate-with-django-orm.md)
  - Text Search
    - [Full-Text Search with SQL](/tidb-cloud/vector-search-full-text-search-sql.md)
    - [Full-Text Search with Python](/tidb-cloud/vector-search-full-text-search-python.md)
    - [Hybrid Search](/tidb-cloud/vector-search-hybrid-search.md)
  - Reference
    - [Vector Data Types](/vector-search/vector-search-data-types.md)
    - [Vector Functions and Operators](/vector-search/vector-search-functions-and-operators.md)
    - [Vector Index](/vector-search/vector-search-index.md)
  - [Improve Performance](/vector-search/vector-search-improve-performance.md)
  - [Limitations](/vector-search/vector-search-limitations.md)
  - [Changelogs](/tidb-cloud/vector-search-changelogs.md)
- Data Service ![BETA](/media/tidb-cloud/blank_transparent_placeholder.png)
  - [Overview](/tidb-cloud/data-service-overview.md)
  - [Get Started](/tidb-cloud/data-service-get-started.md)
  - Chat2Query API
    - [Get Started](/tidb-cloud/use-chat2query-api.md)
    - [Start Multi-round Chat2Query](/tidb-cloud/use-chat2query-sessions.md)
    - [Use Knowledge Bases](/tidb-cloud/use-chat2query-knowledge.md)
  - [Manage Data App](/tidb-cloud/data-service-manage-data-app.md)
  - [Manage Endpoint](/tidb-cloud/data-service-manage-endpoint.md)
  - [API Key](/tidb-cloud/data-service-api-key.md)
  - [Custom Domain](/tidb-cloud/data-service-custom-domain.md)
  - [Integrations](/tidb-cloud/data-service-integrations.md)
  - [Run in Postman](/tidb-cloud/data-service-postman-integration.md)
  - [Deploy Automatically with GitHub](/tidb-cloud/data-service-manage-github-connection.md)
  - [Use OpenAPI Specification with Next.js](/tidb-cloud/data-service-oas-with-nextjs.md)
  - [Data App Configuration Files](/tidb-cloud/data-service-app-config-files.md)
  - [Response and Status Code](/tidb-cloud/data-service-response-and-status-code.md)
- Stream Data
  - [Changefeed Overview](/tidb-cloud/changefeed-overview.md)
  - [To MySQL Sink](/tidb-cloud/changefeed-sink-to-mysql.md)
  - [To Kafka Sink](/tidb-cloud/changefeed-sink-to-apache-kafka.md)
  - [To Pulsar Sink](/tidb-cloud/changefeed-sink-to-apache-pulsar.md)
  - [To TiDB Cloud Sink](/tidb-cloud/changefeed-sink-to-tidb-cloud.md)
  - [To Cloud Storage](/tidb-cloud/changefeed-sink-to-cloud-storage.md)
  - Reference
    - [Set Up Self-Hosted Kafka Private Link Service in AWS](/tidb-cloud/setup-aws-self-hosted-kafka-private-link-service.md)
    - [Set Up Self-Hosted Kafka Private Link Service in Azure](/tidb-cloud/setup-azure-self-hosted-kafka-private-link-service.md)
    - [Set Up Self-Hosted Kafka Private Service Connect in Google Cloud](/tidb-cloud/setup-self-hosted-kafka-private-service-connect.md)
- Disaster Recovery
  - [Recovery Group Overview](/tidb-cloud/recovery-group-overview.md)
  - [Get Started](/tidb-cloud/recovery-group-get-started.md)
  - [Failover and Reprotect Databases](/tidb-cloud/recovery-group-failover.md)
  - [Delete a Recovery Group](/tidb-cloud/recovery-group-delete.md)
- Security
  - Identity Access Control
    - [Password Authentication](/tidb-cloud/tidb-cloud-password-authentication.md)
    - [Standard SSO Authentication](/tidb-cloud/tidb-cloud-sso-authentication.md)
    - [Organization SSO Authentication](/tidb-cloud/tidb-cloud-org-sso-authentication.md)
    - [Identity Access Management](/tidb-cloud/manage-user-access.md)
    - [OAuth 2.0](/tidb-cloud/oauth2.md)
  - Network Access Control
    - {{{ .starter }}}
      - [Connect via Private Endpoint](/tidb-cloud/set-up-private-endpoint-connections-serverless.md)
      - [Configure Firewall Rules for Public Endpoints](/tidb-cloud/configure-serverless-firewall-rules-for-public-endpoints.md)
      - [TLS Connections to {{{ .starter }}}](/tidb-cloud/secure-connections-to-serverless-clusters.md)
    - TiDB Cloud Dedicated
      - [Configure an IP Access List](/tidb-cloud/configure-ip-access-list.md)
      - [Connect via Private Endpoint with AWS](/tidb-cloud/set-up-private-endpoint-connections.md)
      - [Connect via Private Endpoint with Azure](/tidb-cloud/set-up-private-endpoint-connections-on-azure.md)
      - [Connect via Private Endpoint with Google Cloud](/tidb-cloud/set-up-private-endpoint-connections-on-google-cloud.md)
      - [Connect via VPC Peering](/tidb-cloud/set-up-vpc-peering-connections.md)
      - [TLS Connections to TiDB Cloud Dedicated](/tidb-cloud/tidb-cloud-tls-connect-to-dedicated.md)
  - Data Access Control
    - [Encryption at Rest Using Customer-Managed Encryption Keys](/tidb-cloud/tidb-cloud-encrypt-cmek.md)
    - [User-Controlled Log Redaction](/tidb-cloud/tidb-cloud-log-redaction.md)
  - Database Access Control
    - [Configure Cluster Password Settings](/tidb-cloud/configure-security-settings.md)
  - Audit Management
    - [TiDB Cloud Dedicated Database Audit Logging](/tidb-cloud/tidb-cloud-auditing.md)
    - [{{{ .starter }}} Database Audit Logging](/tidb-cloud/serverless-audit-logging.md) ![BETA](/media/tidb-cloud/blank_transparent_placeholder.png)
    - [Console Audit Logging](/tidb-cloud/tidb-cloud-console-auditing.md)
- Billing
  - [Invoices](/tidb-cloud/tidb-cloud-billing.md#invoices)
  - [Billing Details](/tidb-cloud/tidb-cloud-billing.md#billing-details)
  - [Cost Explorer](/tidb-cloud/tidb-cloud-billing.md#cost-explorer)
  - [Billing Profile](/tidb-cloud/tidb-cloud-billing.md#billing-profile)
  - [Credits](/tidb-cloud/tidb-cloud-billing.md#credits)
  - [Payment Method Setting](/tidb-cloud/tidb-cloud-billing.md#payment-method)
  - [Billing from AWS, Azure, or Google Cloud Marketplace](/tidb-cloud/tidb-cloud-billing.md#billing-from-aws-marketplace-azure-marketplace-or-google-cloud-marketplace)
  - [Billing for Changefeed](/tidb-cloud/tidb-cloud-billing-ticdc-rcu.md)
  - [Billing for Data Migration](/tidb-cloud/tidb-cloud-billing-dm.md)
  - [Billing for Recovery Groups](/tidb-cloud/tidb-cloud-billing-recovery-group.md)
  - [Manage Budgets](/tidb-cloud/tidb-cloud-budget.md)
- Integrations
  - [Airbyte](/tidb-cloud/integrate-tidbcloud-with-airbyte.md)
  - [Amazon AppFlow](/develop/dev-guide-aws-appflow-integration.md)
  - [AWS Lambda](/tidb-cloud/integrate-tidbcloud-with-aws-lambda.md)
  - [Cloudflare](/tidb-cloud/integrate-tidbcloud-with-cloudflare.md)
  - [Datadog](/tidb-cloud/monitor-datadog-integration.md)
  - [dbt](/tidb-cloud/integrate-tidbcloud-with-dbt.md)
  - [Gitpod](/develop/dev-guide-playground-gitpod.md)
  - [n8n](/tidb-cloud/integrate-tidbcloud-with-n8n.md)
  - [Netlify](/tidb-cloud/integrate-tidbcloud-with-netlify.md)
  - [New Relic](/tidb-cloud/monitor-new-relic-integration.md)
  - [Prometheus and Grafana](/tidb-cloud/monitor-prometheus-and-grafana-integration.md)
  - [ProxySQL](/develop/dev-guide-proxysql-integration.md)
  - Terraform
    - [Terraform Integration Overview](/tidb-cloud/terraform-tidbcloud-provider-overview.md)
    - [Get TiDB Cloud Terraform Provider](/tidb-cloud/terraform-get-tidbcloud-provider.md)
    - [Use TiDB Cloud Dedicated Cluster Resource](/tidb-cloud/terraform-use-dedicated-cluster-resource.md)
    - [Use TiDB Cloud Dedicated Private Endpoint Connection Resource](/tidb-cloud/terraform-use-dedicated-private-endpoint-connection-resource.md)
    - [Use TiDB Cloud Dedicated VPC Peering Resource](/tidb-cloud/terraform-use-dedicated-vpc-peering-resource.md)
    - [Use TiDB Cloud Dedicated Network Container Resource](/tidb-cloud/terraform-use-dedicated-network-container-resource.md)
    - [Use {{{ .starter }}} Cluster Resource](/tidb-cloud/terraform-use-serverless-cluster-resource.md)
    - [Use {{{ .starter }}} Branch Resource](/tidb-cloud/terraform-use-serverless-branch-resource.md)
    - [Use {{{ .starter }}} Export Resource](/tidb-cloud/terraform-use-serverless-export-resource.md)
    - [Use SQL User Resource](/tidb-cloud/terraform-use-sql-user-resource.md)
    - [Use Cluster Resource (Deprecated)](/tidb-cloud/terraform-use-cluster-resource.md)
    - [Use Backup Resource](/tidb-cloud/terraform-use-backup-resource.md)
    - [Use Restore Resource](/tidb-cloud/terraform-use-restore-resource.md)
    - [Use Import Resource](/tidb-cloud/terraform-use-import-resource.md)
    - [Migrate Cluster Resource](/tidb-cloud/terraform-migrate-cluster-resource.md)
  - [Vercel](/tidb-cloud/integrate-tidbcloud-with-vercel.md)
  - [Zapier](/tidb-cloud/integrate-tidbcloud-with-zapier.md)

## REFERENCE

- SQL Reference
  - [Explore SQL with TiDB](/basic-sql-operations.md)
  - SQL Language Structure and Syntax
    - Attributes
      - [AUTO_INCREMENT](/auto-increment.md)
      - [AUTO_RANDOM](/auto-random.md)
      - [SHARD_ROW_ID_BITS](/shard-row-id-bits.md)
    - [Literal Values](/literal-values.md)
    - [Schema Object Names](/schema-object-names.md)
    - [Keywords and Reserved Words](/keywords.md)
    - [User-Defined Variables](/user-defined-variables.md)
    - [Expression Syntax](/expression-syntax.md)
    - [Comment Syntax](/comment-syntax.md)
  - SQL Statements
    - [Overview](/sql-statements/sql-statement-overview.md)
    - [`ADMIN`](/sql-statements/sql-statement-admin.md)
    - [`ADMIN ALTER DDL JOBS`](/sql-statements/sql-statement-admin-alter-ddl.md)
    - [`ADMIN CANCEL DDL`](/sql-statements/sql-statement-admin-cancel-ddl.md)
    - [`ADMIN CHECKSUM TABLE`](/sql-statements/sql-statement-admin-checksum-table.md)
    - [`ADMIN CHECK [TABLE|INDEX]`](/sql-statements/sql-statement-admin-check-table-index.md)
    - [`ADMIN CLEANUP INDEX`](/sql-statements/sql-statement-admin-cleanup.md)
    - [`ADMIN PAUSE DDL`](/sql-statements/sql-statement-admin-pause-ddl.md)
    - [`ADMIN RECOVER INDEX`](/sql-statements/sql-statement-admin-recover.md)
    - [`ADMIN RESUME DDL`](/sql-statements/sql-statement-admin-resume-ddl.md)
    - [`ADMIN SHOW DDL [JOBS|JOB QUERIES]`](/sql-statements/sql-statement-admin-show-ddl.md)
    - [`ALTER DATABASE`](/sql-statements/sql-statement-alter-database.md)
    - [`ALTER INSTANCE`](/sql-statements/sql-statement-alter-instance.md)
    - [`ALTER PLACEMENT POLICY`](/sql-statements/sql-statement-alter-placement-policy.md)
    - [`ALTER RANGE`](/sql-statements/sql-statement-alter-range.md)
    - [`ALTER RESOURCE GROUP`](/sql-statements/sql-statement-alter-resource-group.md)
    - [`ALTER SEQUENCE`](/sql-statements/sql-statement-alter-sequence.md)
    - `ALTER TABLE`
      - [Overview](/sql-statements/sql-statement-alter-table.md)
      - [`ADD COLUMN`](/sql-statements/sql-statement-add-column.md)
      - [`ADD INDEX`](/sql-statements/sql-statement-add-index.md)
      - [`ALTER INDEX`](/sql-statements/sql-statement-alter-index.md)
      - [`CHANGE COLUMN`](/sql-statements/sql-statement-change-column.md)
      - [`COMPACT`](/sql-statements/sql-statement-alter-table-compact.md)
      - [`DROP COLUMN`](/sql-statements/sql-statement-drop-column.md)
      - [`DROP INDEX`](/sql-statements/sql-statement-drop-index.md)
      - [`MODIFY COLUMN`](/sql-statements/sql-statement-modify-column.md)
      - [`RENAME INDEX`](/sql-statements/sql-statement-rename-index.md)
    - [`ALTER USER`](/sql-statements/sql-statement-alter-user.md)
    - [`ANALYZE TABLE`](/sql-statements/sql-statement-analyze-table.md)
    - [`BACKUP`](/sql-statements/sql-statement-backup.md)
    - [`BATCH`](/sql-statements/sql-statement-batch.md)
    - [`BEGIN`](/sql-statements/sql-statement-begin.md)
    - [`CANCEL IMPORT JOB`](/sql-statements/sql-statement-cancel-import-job.md)
    - [`COMMIT`](/sql-statements/sql-statement-commit.md)
    - [`CREATE [GLOBAL|SESSION] BINDING`](/sql-statements/sql-statement-create-binding.md)
    - [`CREATE DATABASE`](/sql-statements/sql-statement-create-database.md)
    - [`CREATE INDEX`](/sql-statements/sql-statement-create-index.md)
    - [`CREATE PLACEMENT POLICY`](/sql-statements/sql-statement-create-placement-policy.md)
    - [`CREATE RESOURCE GROUP`](/sql-statements/sql-statement-create-resource-group.md)
    - [`CREATE ROLE`](/sql-statements/sql-statement-create-role.md)
    - [`CREATE SEQUENCE`](/sql-statements/sql-statement-create-sequence.md)
    - [`CREATE TABLE LIKE`](/sql-statements/sql-statement-create-table-like.md)
    - [`CREATE TABLE`](/sql-statements/sql-statement-create-table.md)
    - [`CREATE USER`](/sql-statements/sql-statement-create-user.md)
    - [`CREATE VIEW`](/sql-statements/sql-statement-create-view.md)
    - [`DEALLOCATE`](/sql-statements/sql-statement-deallocate.md)
    - [`DELETE`](/sql-statements/sql-statement-delete.md)
    - [`DESC`](/sql-statements/sql-statement-desc.md)
    - [`DESCRIBE`](/sql-statements/sql-statement-describe.md)
    - [`DO`](/sql-statements/sql-statement-do.md)
    - [`DROP [GLOBAL|SESSION] BINDING`](/sql-statements/sql-statement-drop-binding.md)
    - [`DROP DATABASE`](/sql-statements/sql-statement-drop-database.md)
    - [`DROP PLACEMENT POLICY`](/sql-statements/sql-statement-drop-placement-policy.md)
    - [`DROP RESOURCE GROUP`](/sql-statements/sql-statement-drop-resource-group.md)
    - [`DROP ROLE`](/sql-statements/sql-statement-drop-role.md)
    - [`DROP SEQUENCE`](/sql-statements/sql-statement-drop-sequence.md)
    - [`DROP STATS`](/sql-statements/sql-statement-drop-stats.md)
    - [`DROP TABLE`](/sql-statements/sql-statement-drop-table.md)
    - [`DROP USER`](/sql-statements/sql-statement-drop-user.md)
    - [`DROP VIEW`](/sql-statements/sql-statement-drop-view.md)
    - [`EXECUTE`](/sql-statements/sql-statement-execute.md)
    - [`EXPLAIN ANALYZE`](/sql-statements/sql-statement-explain-analyze.md)
    - [`EXPLAIN`](/sql-statements/sql-statement-explain.md)
    - [`FLASHBACK CLUSTER`](/sql-statements/sql-statement-flashback-cluster.md)
    - [`FLASHBACK DATABASE`](/sql-statements/sql-statement-flashback-database.md)
    - [`FLASHBACK TABLE`](/sql-statements/sql-statement-flashback-table.md)
    - [`FLUSH PRIVILEGES`](/sql-statements/sql-statement-flush-privileges.md)
    - [`FLUSH STATUS`](/sql-statements/sql-statement-flush-status.md)
    - [`FLUSH TABLES`](/sql-statements/sql-statement-flush-tables.md)
    - [`GRANT <privileges>`](/sql-statements/sql-statement-grant-privileges.md)
    - [`GRANT <role>`](/sql-statements/sql-statement-grant-role.md)
    - [`IMPORT INTO`](/sql-statements/sql-statement-import-into.md)
    - [`INSERT`](/sql-statements/sql-statement-insert.md)
    - [`KILL [TIDB]`](/sql-statements/sql-statement-kill.md)
    - [`LOAD DATA`](/sql-statements/sql-statement-load-data.md)
    - [`LOAD STATS`](/sql-statements/sql-statement-load-stats.md)
    - [`LOCK STATS`](/sql-statements/sql-statement-lock-stats.md)
    - [`LOCK TABLES` and `UNLOCK TABLES`](/sql-statements/sql-statement-lock-tables-and-unlock-tables.md)
    - [`PREPARE`](/sql-statements/sql-statement-prepare.md)
    - [`QUERY WATCH`](/sql-statements/sql-statement-query-watch.md)
    - [`RECOVER TABLE`](/sql-statements/sql-statement-recover-table.md)
    - [`RENAME TABLE`](/sql-statements/sql-statement-rename-table.md)
    - [`RENAME USER`](/sql-statements/sql-statement-rename-user.md)
    - [`REPLACE`](/sql-statements/sql-statement-replace.md)
    - [`RESTORE`](/sql-statements/sql-statement-restore.md)
    - [`REVOKE <privileges>`](/sql-statements/sql-statement-revoke-privileges.md)
    - [`REVOKE <role>`](/sql-statements/sql-statement-revoke-role.md)
    - [`ROLLBACK`](/sql-statements/sql-statement-rollback.md)
    - [`SAVEPOINT`](/sql-statements/sql-statement-savepoint.md)
    - [`SELECT`](/sql-statements/sql-statement-select.md)
    - [`SET DEFAULT ROLE`](/sql-statements/sql-statement-set-default-role.md)
    - [`SET [NAMES|CHARACTER SET]`](/sql-statements/sql-statement-set-names.md)
    - [`SET PASSWORD`](/sql-statements/sql-statement-set-password.md)
    - [`SET RESOURCE GROUP`](/sql-statements/sql-statement-set-resource-group.md)
    - [`SET ROLE`](/sql-statements/sql-statement-set-role.md)
    - [`SET TRANSACTION`](/sql-statements/sql-statement-set-transaction.md)
    - [`SET [GLOBAL|SESSION] <variable>`](/sql-statements/sql-statement-set-variable.md)
    - [`SHOW ANALYZE STATUS`](/sql-statements/sql-statement-show-analyze-status.md)
    - [`SHOW [BACKUPS|RESTORES]`](/sql-statements/sql-statement-show-backups.md)
    - [`SHOW [GLOBAL|SESSION] BINDINGS`](/sql-statements/sql-statement-show-bindings.md)
    - [`SHOW BUILTINS`](/sql-statements/sql-statement-show-builtins.md)
    - [`SHOW CHARACTER SET`](/sql-statements/sql-statement-show-character-set.md)
    - [`SHOW COLLATION`](/sql-statements/sql-statement-show-collation.md)
    - [`SHOW COLUMN_STATS_USAGE`](/sql-statements/sql-statement-show-column-stats-usage.md)
    - [`SHOW COLUMNS FROM`](/sql-statements/sql-statement-show-columns-from.md)
    - [`SHOW CREATE DATABASE`](/sql-statements/sql-statement-show-create-database.md)
    - [`SHOW CREATE PLACEMENT POLICY`](/sql-statements/sql-statement-show-create-placement-policy.md)
    - [`SHOW CREATE RESOURCE GROUP`](/sql-statements/sql-statement-show-create-resource-group.md)
    - [`SHOW CREATE SEQUENCE`](/sql-statements/sql-statement-show-create-sequence.md)
    - [`SHOW CREATE TABLE`](/sql-statements/sql-statement-show-create-table.md)
    - [`SHOW CREATE USER`](/sql-statements/sql-statement-show-create-user.md)
    - [`SHOW DATABASES`](/sql-statements/sql-statement-show-databases.md)
    - [`SHOW ENGINES`](/sql-statements/sql-statement-show-engines.md)
    - [`SHOW ERRORS`](/sql-statements/sql-statement-show-errors.md)
    - [`SHOW FIELDS FROM`](/sql-statements/sql-statement-show-fields-from.md)
    - [`SHOW GRANTS`](/sql-statements/sql-statement-show-grants.md)
    - [`SHOW IMPORT JOB`](/sql-statements/sql-statement-show-import-job.md)
    - [`SHOW INDEXES [FROM|IN]`](/sql-statements/sql-statement-show-indexes.md)
    - [`SHOW MASTER STATUS`](/sql-statements/sql-statement-show-master-status.md)
    - [`SHOW PLACEMENT`](/sql-statements/sql-statement-show-placement.md)
    - [`SHOW PLACEMENT FOR`](/sql-statements/sql-statement-show-placement-for.md)
    - [`SHOW PLACEMENT LABELS`](/sql-statements/sql-statement-show-placement-labels.md)
    - [`SHOW PLUGINS`](/sql-statements/sql-statement-show-plugins.md)
    - [`SHOW PRIVILEGES`](/sql-statements/sql-statement-show-privileges.md)
    - [`SHOW PROCESSLIST`](/sql-statements/sql-statement-show-processlist.md)
    - [`SHOW PROFILES`](/sql-statements/sql-statement-show-profiles.md)
    - [`SHOW SCHEMAS`](/sql-statements/sql-statement-show-schemas.md)
    - [`SHOW STATS_BUCKETS`](/sql-statements/sql-statement-show-stats-buckets.md)
    - [`SHOW STATS_HEALTHY`](/sql-statements/sql-statement-show-stats-healthy.md)
    - [`SHOW STATS_HISTOGRAMS`](/sql-statements/sql-statement-show-stats-histograms.md)
    - [`SHOW STATS_LOCKED`](/sql-statements/sql-statement-show-stats-locked.md)
    - [`SHOW STATS_META`](/sql-statements/sql-statement-show-stats-meta.md)
    - [`SHOW STATS_TOPN`](/sql-statements/sql-statement-show-stats-topn.md)
    - [`SHOW STATUS`](/sql-statements/sql-statement-show-status.md)
    - [`SHOW TABLE NEXT_ROW_ID`](/sql-statements/sql-statement-show-table-next-rowid.md)
    - [`SHOW TABLE REGIONS`](/sql-statements/sql-statement-show-table-regions.md)
    - [`SHOW TABLE STATUS`](/sql-statements/sql-statement-show-table-status.md)
    - [`SHOW TABLES`](/sql-statements/sql-statement-show-tables.md)
    - [`SHOW [GLOBAL|SESSION] VARIABLES`](/sql-statements/sql-statement-show-variables.md)
    - [`SHOW WARNINGS`](/sql-statements/sql-statement-show-warnings.md)
    - [`SPLIT REGION`](/sql-statements/sql-statement-split-region.md)
    - [`START TRANSACTION`](/sql-statements/sql-statement-start-transaction.md)
    - [`TABLE`](/sql-statements/sql-statement-table.md)
    - [`TRACE`](/sql-statements/sql-statement-trace.md)
    - [`TRUNCATE`](/sql-statements/sql-statement-truncate.md)
    - [`UNLOCK STATS`](/sql-statements/sql-statement-unlock-stats.md)
    - [`UPDATE`](/sql-statements/sql-statement-update.md)
    - [`USE`](/sql-statements/sql-statement-use.md)
    - [`WITH`](/sql-statements/sql-statement-with.md)
  - Data Types
    - [Overview](/data-type-overview.md)
    - [Default Values](/data-type-default-values.md)
    - [Numeric Types](/data-type-numeric.md)
    - [Date and Time Types](/data-type-date-and-time.md)
    - [String Types](/data-type-string.md)
    - [JSON Type](/data-type-json.md)
  - Functions and Operators
    - [Overview](/functions-and-operators/functions-and-operators-overview.md)
    - [Type Conversion in Expression Evaluation](/functions-and-operators/type-conversion-in-expression-evaluation.md)
    - [Operators](/functions-and-operators/operators.md)
    - [Control Flow Functions](/functions-and-operators/control-flow-functions.md)
    - [String Functions](/functions-and-operators/string-functions.md)
    - [Numeric Functions and Operators](/functions-and-operators/numeric-functions-and-operators.md)
    - [Date and Time Functions](/functions-and-operators/date-and-time-functions.md)
    - [Bit Functions and Operators](/functions-and-operators/bit-functions-and-operators.md)
    - [Cast Functions and Operators](/functions-and-operators/cast-functions-and-operators.md)
    - [Encryption and Compression Functions](/functions-and-operators/encryption-and-compression-functions.md)
    - [Locking Functions](/functions-and-operators/locking-functions.md)
    - [Information Functions](/functions-and-operators/information-functions.md)
    - JSON Functions
      - [Overview](/functions-and-operators/json-functions.md)
      - [Functions That Create JSON](/functions-and-operators/json-functions/json-functions-create.md)
      - [Functions That Search JSON](/functions-and-operators/json-functions/json-functions-search.md)
      - [Functions That Modify JSON](/functions-and-operators/json-functions/json-functions-modify.md)
      - [Functions That Return JSON](/functions-and-operators/json-functions/json-functions-return.md)
      - [JSON Utility Functions](/functions-and-operators/json-functions/json-functions-utility.md)
      - [Functions That Aggregate JSON](/functions-and-operators/json-functions/json-functions-aggregate.md)
      - [Functions That Validate JSON](/functions-and-operators/json-functions/json-functions-validate.md)
    - [Aggregate (GROUP BY) Functions](/functions-and-operators/aggregate-group-by-functions.md)
    - [GROUP BY Modifiers](/functions-and-operators/group-by-modifier.md)
    - [Window Functions](/functions-and-operators/window-functions.md)
    - [Sequence Functions](/functions-and-operators/sequence-functions.md)
    - [Utility Functions](/functions-and-operators/utility-functions.md)
    - [Miscellaneous Functions](/functions-and-operators/miscellaneous-functions.md)
    - [TiDB Specific Functions](/functions-and-operators/tidb-functions.md)
    - [Utility Functions](/functions-and-operators/utility-functions.md)
    - [Precision Math](/functions-and-operators/precision-math.md)
    - [Set Operations](/functions-and-operators/set-operators.md)
    - [List of Expressions for Pushdown](/functions-and-operators/expressions-pushed-down.md)
  - [Clustered Indexes](/clustered-indexes.md)
  - [Constraints](/constraints.md)
  - [Generated Columns](/generated-columns.md)
  - [SQL Mode](/sql-mode.md)
  - [Table Attributes](/table-attributes.md)
  - Transactions
    - [Overview](/transaction-overview.md)
    - [Isolation Levels](/transaction-isolation-levels.md)
    - [Optimistic Transactions](/optimistic-transaction.md)
    - [Pessimistic Transactions](/pessimistic-transaction.md)
    - [Non-Transactional DML Statements](/non-transactional-dml.md)
    - [Pipelined DML](/pipelined-dml.md)
  - [Views](/views.md)
  - [Partitioning](/partitioned-table.md)
  - [Temporary Tables](/temporary-tables.md)
  - [Cached Tables](/cached-tables.md)
  - [FOREIGN KEY Constraints](/foreign-key.md)
  - Character Set and Collation
    - [Overview](/character-set-and-collation.md)
    - [GBK](/character-set-gbk.md)
  - Read Historical Data
    - Use Stale Read (Recommended)
      - [Usage Scenarios of Stale Read](/stale-read.md)
      - [Perform Stale Read Using `As OF TIMESTAMP`](/as-of-timestamp.md)
      - [Perform Stale Read Using `tidb_read_staleness`](/tidb-read-staleness.md)
      - [Perform Stale Read Using `tidb_external_ts`](/tidb-external-ts.md)
    - [Use the `tidb_snapshot` System Variable](/read-historical-data.md)
  - [Placement Rules in SQL](/placement-rules-in-sql.md)
  - System Tables
    - `mysql` Schema
      - [Overview](/mysql-schema/mysql-schema.md)
      - [`tidb_mdl_view`](/mysql-schema/mysql-schema-tidb-mdl-view.md)
      - [`user`](/mysql-schema/mysql-schema-user.md)
    - INFORMATION_SCHEMA
      - [Overview](/information-schema/information-schema.md)
      - [`ANALYZE_STATUS`](/information-schema/information-schema-analyze-status.md)
      - [`CHECK_CONSTRAINTS`](/information-schema/information-schema-check-constraints.md)
      - [`CLIENT_ERRORS_SUMMARY_BY_HOST`](/information-schema/client-errors-summary-by-host.md)
      - [`CLIENT_ERRORS_SUMMARY_BY_USER`](/information-schema/client-errors-summary-by-user.md)
      - [`CLIENT_ERRORS_SUMMARY_GLOBAL`](/information-schema/client-errors-summary-global.md)
      - [`CHARACTER_SETS`](/information-schema/information-schema-character-sets.md)
      - [`CLUSTER_INFO`](/information-schema/information-schema-cluster-info.md)
      - [`COLLATIONS`](/information-schema/information-schema-collations.md)
      - [`COLLATION_CHARACTER_SET_APPLICABILITY`](/information-schema/information-schema-collation-character-set-applicability.md)
      - [`COLUMNS`](/information-schema/information-schema-columns.md)
      - [`DATA_LOCK_WAITS`](/information-schema/information-schema-data-lock-waits.md)
      - [`DDL_JOBS`](/information-schema/information-schema-ddl-jobs.md)
      - [`DEADLOCKS`](/information-schema/information-schema-deadlocks.md)
      - [`ENGINES`](/information-schema/information-schema-engines.md)
      - [`KEYWORDS`](/information-schema/information-schema-keywords.md)
      - [`KEY_COLUMN_USAGE`](/information-schema/information-schema-key-column-usage.md)
      - [`MEMORY_USAGE`](/information-schema/information-schema-memory-usage.md)
      - [`MEMORY_USAGE_OPS_HISTORY`](/information-schema/information-schema-memory-usage-ops-history.md)
      - [`PARTITIONS`](/information-schema/information-schema-partitions.md)
      - [`PLACEMENT_POLICIES`](/information-schema/information-schema-placement-policies.md)
      - [`PROCESSLIST`](/information-schema/information-schema-processlist.md)
      - [`REFERENTIAL_CONSTRAINTS`](/information-schema/information-schema-referential-constraints.md)
      - [`RESOURCE_GROUPS`](/information-schema/information-schema-resource-groups.md)
      - [`RUNAWAY_WATCHES`](/information-schema/information-schema-runaway-watches.md)
      - [`SCHEMATA`](/information-schema/information-schema-schemata.md)
      - [`SEQUENCES`](/information-schema/information-schema-sequences.md)
      - [`SESSION_VARIABLES`](/information-schema/information-schema-session-variables.md)
      - [`SLOW_QUERY`](/information-schema/information-schema-slow-query.md)
      - [`STATISTICS`](/information-schema/information-schema-statistics.md)
      - [`TABLES`](/information-schema/information-schema-tables.md)
      - [`TABLE_CONSTRAINTS`](/information-schema/information-schema-table-constraints.md)
      - [`TABLE_STORAGE_STATS`](/information-schema/information-schema-table-storage-stats.md)
      - [`TIDB_CHECK_CONSTRAINTS`](/information-schema/information-schema-tidb-check-constraints.md)
      - [`TIDB_HOT_REGIONS_HISTORY`](/information-schema/information-schema-tidb-hot-regions-history.md)
      - [`TIDB_INDEXES`](/information-schema/information-schema-tidb-indexes.md)
      - [`TIDB_INDEX_USAGE`](/information-schema/information-schema-tidb-index-usage.md)
      - [`TIDB_SERVERS_INFO`](/information-schema/information-schema-tidb-servers-info.md)
      - [`TIDB_TRX`](/information-schema/information-schema-tidb-trx.md)
      - [`TIFLASH_REPLICA`](/information-schema/information-schema-tiflash-replica.md)
      - [`TIFLASH_SEGMENTS`](/information-schema/information-schema-tiflash-segments.md)
      - [`TIFLASH_TABLES`](/information-schema/information-schema-tiflash-tables.md)
      - [`TIKV_REGION_PEERS`](/information-schema/information-schema-tikv-region-peers.md)
      - [`TIKV_REGION_STATUS`](/information-schema/information-schema-tikv-region-status.md)
      - [`TIKV_STORE_STATUS`](/information-schema/information-schema-tikv-store-status.md)
      - [`USER_ATTRIBUTES`](/information-schema/information-schema-user-attributes.md)
      - [`USER_PRIVILEGES`](/information-schema/information-schema-user-privileges.md)
      - [`VARIABLES_INFO`](/information-schema/information-schema-variables-info.md)
      - [`VIEWS`](/information-schema/information-schema-views.md)
    - PERFORMANCE_SCHEMA
      - [Overview](/performance-schema/performance-schema.md)
      - [`SESSION_CONNECT_ATTRS`](/performance-schema/performance-schema-session-connect-attrs.md)
    - SYS
      - [Overview](/sys-schema/sys-schema.md)
      - [`schema_unused_indexes`](/sys-schema/sys-schema-unused-indexes.md)
  - [Metadata Lock](/metadata-lock.md)
  - [Use UUIDs](/best-practices/uuid.md)
  - [TiDB Accelerated Table Creation](/accelerated-table-creation.md)
  - [Schema Cache](/schema-cache.md)
- API Reference ![BETA](/media/tidb-cloud/blank_transparent_placeholder.png)
  - [Overview](/tidb-cloud/api-overview.md)
  - v1beta1
    - [Billing](https://docs.pingcap.com/tidbcloud/api/v1beta1/billing)
    - [Data Service](https://docs.pingcap.com/tidbcloud/api/v1beta1/dataservice)
    - [IAM](https://docs.pingcap.com/tidbcloud/api/v1beta1/iam)
    - [MSP (Deprecated)](https://docs.pingcap.com/tidbcloud/api/v1beta1/msp)
  - [v1beta](https://docs.pingcap.com/tidbcloud/api/v1beta)
- CLI Reference ![BETA](/media/tidb-cloud/blank_transparent_placeholder.png)
  - [Overview](/tidb-cloud/cli-reference.md)
  - auth
    - [login](/tidb-cloud/ticloud-auth-login.md)
    - [logout](/tidb-cloud/ticloud-auth-logout.md)
    - [whoami](/tidb-cloud/ticloud-auth-whoami.md)
  - serverless
    - [create](/tidb-cloud/ticloud-cluster-create.md)
    - [delete](/tidb-cloud/ticloud-cluster-delete.md)
    - [describe](/tidb-cloud/ticloud-cluster-describe.md)
    - [list](/tidb-cloud/ticloud-cluster-list.md)
    - [update](/tidb-cloud/ticloud-serverless-update.md)
    - [spending-limit](/tidb-cloud/ticloud-serverless-spending-limit.md)
    - [region](/tidb-cloud/ticloud-serverless-region.md)
    - [shell](/tidb-cloud/ticloud-serverless-shell.md)
    - branch
      - [create](/tidb-cloud/ticloud-branch-create.md)
      - [delete](/tidb-cloud/ticloud-branch-delete.md)
      - [describe](/tidb-cloud/ticloud-branch-describe.md)
      - [list](/tidb-cloud/ticloud-branch-list.md)
      - [shell](/tidb-cloud/ticloud-branch-shell.md)
    - import
      - [cancel](/tidb-cloud/ticloud-import-cancel.md)
      - [describe](/tidb-cloud/ticloud-import-describe.md)
      - [list](/tidb-cloud/ticloud-import-list.md)
      - [start](/tidb-cloud/ticloud-import-start.md)
    - export
      - [create](/tidb-cloud/ticloud-serverless-export-create.md)
      - [describe](/tidb-cloud/ticloud-serverless-export-describe.md)
      - [list](/tidb-cloud/ticloud-serverless-export-list.md)
      - [cancel](/tidb-cloud/ticloud-serverless-export-cancel.md)
      - [download](/tidb-cloud/ticloud-serverless-export-download.md)
    - sql-user
      - [create](/tidb-cloud/ticloud-serverless-sql-user-create.md)
      - [delete](/tidb-cloud/ticloud-serverless-sql-user-delete.md)
      - [list](/tidb-cloud/ticloud-serverless-sql-user-list.md)
      - [update](/tidb-cloud/ticloud-serverless-sql-user-update.md)
    - audit-log
      - [config](/tidb-cloud/ticloud-auditlog-config.md)
      - [describe](/tidb-cloud/ticloud-auditlog-describe.md)
      - [download](/tidb-cloud/ticloud-auditlog-download.md)
      - filter-rule
        - [create](/tidb-cloud/ticloud-auditlog-filter-create.md)
        - [delete](/tidb-cloud/ticloud-auditlog-filter-delete.md)
        - [describe](/tidb-cloud/ticloud-auditlog-filter-describe.md)
        - [list](/tidb-cloud/ticloud-auditlog-filter-list.md)
        - [template](/tidb-cloud/ticloud-auditlog-filter-template.md)
        - [update](/tidb-cloud/ticloud-auditlog-filter-update.md)
    - authorized-network
      - [create](/tidb-cloud/ticloud-serverless-authorized-network-create.md)
      - [delete](/tidb-cloud/ticloud-serverless-authorized-network-delete.md)
      - [list](/tidb-cloud/ticloud-serverless-authorized-network-list.md)
      - [update](/tidb-cloud/ticloud-serverless-authorized-network-update.md)
  - [ai](/tidb-cloud/ticloud-ai.md)
  - [completion](/tidb-cloud/ticloud-completion.md)
  - config
    - [create](/tidb-cloud/ticloud-config-create.md)
    - [delete](/tidb-cloud/ticloud-config-delete.md)
    - [describe](/tidb-cloud/ticloud-config-describe.md)
    - [edit](/tidb-cloud/ticloud-config-edit.md)
    - [list](/tidb-cloud/ticloud-config-list.md)
    - [set](/tidb-cloud/ticloud-config-set.md)
    - [use](/tidb-cloud/ticloud-config-use.md)
  - project
    - [list](/tidb-cloud/ticloud-project-list.md)
  - [upgrade](/tidb-cloud/ticloud-upgrade.md)
  - [help](/tidb-cloud/ticloud-help.md)
- General Reference
  - TiDB Cluster Architecture
    - [Overview](/tidb-architecture.md)
    - [Storage](/tidb-storage.md)
    - [Computing](/tidb-computing.md)
    - [Scheduling](/tidb-scheduling.md)
    - [TSO](/tso.md)
  - Storage Engines
    - TiKV
      - [TiKV Overview](/tikv-overview.md)
      - [RocksDB Overview](/storage-engine/rocksdb-overview.md)
    - TiFlash
      - [TiFlash Overview](/tiflash/tiflash-overview.md)
      - [Spill to Disk](/tiflash/tiflash-spill-disk.md)
  - TiDB Cloud Partner Web Console
    - [TiDB Cloud Partners](/tidb-cloud/tidb-cloud-partners.md)
    - [MSP Customer](/tidb-cloud/managed-service-provider-customer.md)
    - [Reseller's Customer](/tidb-cloud/cppo-customer.md)
  - TiDB Distributed eXecution Framework (DXF)
    - [Introduction](/tidb-distributed-execution-framework.md)
    - [TiDB Global Sort](/tidb-global-sort.md)
  - [TiDB Cloud Dedicated Limitations and Quotas](/tidb-cloud/limitations-and-quotas.md)
  - [{{{ .starter }}} Limitations](/tidb-cloud/serverless-limitations.md)
  - [Limited SQL Features on TiDB Cloud](/tidb-cloud/limited-sql-features.md)
  - [TiDB Limitations](/tidb-limitations.md)
  - Benchmarks
    - TiDB v8.5
      - [Performance Highlights](/tidb-cloud/v8.5-performance-highlights.md)
      - [TPC-C Performance Test Report](/tidb-cloud/v8.5-performance-benchmarking-with-tpcc.md)
      - [Sysbench Performance Test Report](/tidb-cloud/v8.5-performance-benchmarking-with-sysbench.md)
    - TiDB v8.1
      - [TPC-C Performance Test Report](/tidb-cloud/v8.1-performance-benchmarking-with-tpcc.md)
      - [Sysbench Performance Test Report](/tidb-cloud/v8.1-performance-benchmarking-with-sysbench.md)
    - TiDB v7.5
      - [TPC-C Performance Test Report](/tidb-cloud/v7.5-performance-benchmarking-with-tpcc.md)
      - [Sysbench Performance Test Report](/tidb-cloud/v7.5-performance-benchmarking-with-sysbench.md)
    - TiDB v7.1
      - [TPC-C Performance Test Report](/tidb-cloud/v7.1-performance-benchmarking-with-tpcc.md)
      - [Sysbench Performance Test Report](/tidb-cloud/v7.1-performance-benchmarking-with-sysbench.md)
    - TiDB v6.5
      - [TPC-C Performance Test Report](/tidb-cloud/v6.5-performance-benchmarking-with-tpcc.md)
      - [Sysbench Performance Test Report](/tidb-cloud/v6.5-performance-benchmarking-with-sysbench.md)
  - [System Variables](/system-variables.md)
  - [Server Status Variables](/status-variables.md)
  - [Table Filter](/table-filter.md)
  - [URI Formats of External Storage Services](/external-storage-uri.md)
  - [DDL Execution Principles and Best Practices](/ddl-introduction.md)
  - [Batch Processing](/batch-processing.md)
  - [Troubleshoot Inconsistency Between Data and Indexes](/troubleshoot-data-inconsistency-errors.md)
  - [Notifications](/tidb-cloud/notifications.md)
  - [Glossary](/tidb-cloud/tidb-cloud-glossary.md)
- Support Plan
  - [Connected Care Overview](/tidb-cloud/connected-care-overview.md)
  - [Connected Care Details](/tidb-cloud/connected-care-detail.md)
  - Connected Care Support Service Features
    - [Connected: Clinic Service](/tidb-cloud/tidb-cloud-clinic.md)
    - [Connected: AI Chat in IM](/tidb-cloud/connected-ai-chat-in-im.md)
    - Connected: IM Subscription for TiDB Cloud Alerts
      - [Subscribe via Slack](/tidb-cloud/monitor-alert-slack.md)
      - [Subscribe via Zoom](/tidb-cloud/monitor-alert-zoom.md)
    - Connected: IM Ticket Creation and Update Subscription
      - [Create Tickets and Subscribe to Ticket Updates via Slack](/tidb-cloud/connected-slack-ticket-creation.md)
      - [Create Tickets and Subscribe to Ticket Updates via Lark](/tidb-cloud/connected-lark-ticket-creation.md)
    - Connected: IM Interaction for Support Tickets
      - [Interact with Support Tickets via Slack](/tidb-cloud/connected-slack-ticket-interaction.md)
      - [Interact with Support Tickets via Lark](/tidb-cloud/connected-lark-ticket-interaction.md)
  - [Get Support](/tidb-cloud/tidb-cloud-support.md)
- FAQs
  - [TiDB Cloud FAQs](/tidb-cloud/tidb-cloud-faq.md)
  - [{{{ .starter }}} FAQs](/tidb-cloud/serverless-faqs.md)

## RELEASES

- Release Notes
  - [2025](/tidb-cloud/tidb-cloud-release-notes.md)
  - [2024](/tidb-cloud/release-notes-2024.md)
  - [2023](/tidb-cloud/release-notes-2023.md)
  - [2022](/tidb-cloud/release-notes-2022.md)
  - [2021](/tidb-cloud/release-notes-2021.md)
  - [2020](/tidb-cloud/release-notes-2020.md)
- Maintenance Notification
  - [[2024-09-15] TiDB Cloud Console Maintenance Notification](/tidb-cloud/notification-2024-09-15-console-maintenance.md)
  - [[2024-04-18] TiDB Cloud Data Migration (DM) Feature Maintenance Notification](/tidb-cloud/notification-2024-04-18-dm-feature-maintenance.md)
  - [[2024-04-16] TiDB Cloud Monitoring Features Maintenance Notification](/tidb-cloud/notification-2024-04-16-monitoring-features-maintenance.md)
  - [[2024-04-11] TiDB Cloud Data Migration (DM) Feature Maintenance Notification](/tidb-cloud/notification-2024-04-11-dm-feature-maintenance.md)
  - [[2024-04-09] TiDB Cloud Monitoring Features Maintenance Notification](/tidb-cloud/notification-2024-04-09-monitoring-features-maintenance.md)
  - [[2023-11-14] TiDB Cloud Dedicated Scale Feature Maintenance Notification](/tidb-cloud/notification-2023-11-14-scale-feature-maintenance.md)
  - [[2023-09-26] TiDB Cloud Console Maintenance Notification](/tidb-cloud/notification-2023-09-26-console-maintenance.md)
  - [[2023-08-31] TiDB Cloud Console Maintenance Notification](/tidb-cloud/notification-2023-08-31-console-maintenance.md)
