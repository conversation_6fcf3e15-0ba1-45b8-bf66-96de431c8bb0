---
title: List of Expressions for Pushdown
summary: Learn a list of expressions that can be pushed down to TiKV and the related operations.
---

# List of Expressions for Pushdown

When TiDB reads data from TiKV, TiDB tries to push down some expressions (including calculations of functions or operators) to be processed to TiKV. This reduces the amount of transferred data and offloads processing from a single TiDB node. This document introduces the expressions that TiDB already supports pushing down and how to prohibit specific expressions from being pushed down using blocklist.

TiFlash also supports pushdown for the functions and operators [listed on this page](/tiflash/tiflash-supported-pushdown-calculations.md).

> **Note:**
>
> The Aggregate functions do not support pushdown to TiKV when used as [window functions](/functions-and-operators/window-functions.md).

## Supported expressions for pushdown to TiKV

| Expression Type | Operations |
| :-------------- | :------------------------------------- |
| [Logical operators](/functions-and-operators/operators.md#logical-operators) | AND (&&) <br/>OR (&#124;&#124;) <br/>NOT (!) <br/>XOR |
| [Bit operators](/functions-and-operators/operators.md#operators) | [&](https://dev.mysql.com/doc/refman/8.0/en/bit-functions.html#operator_bitwise-and) <br/>[~](https://dev.mysql.com/doc/refman/8.0/en/bit-functions.html#operator_bitwise-invert) <br/>[\|](https://dev.mysql.com/doc/refman/8.0/en/bit-functions.html#operator_bitwise-or) <br/>[`^`](https://dev.mysql.com/doc/refman/8.0/en/bit-functions.html#operator_bitwise-xor) <br/>[`<<`](https://dev.mysql.com/doc/refman/8.0/en/bit-functions.html#operator_left-shift) <br/>[`>>`](https://dev.mysql.com/doc/refman/8.0/en/bit-functions.html#operator_right-shift) |
| [Comparison functions and operators](/functions-and-operators/operators.md#comparison-functions-and-operators) | [`<`](https://dev.mysql.com/doc/refman/8.0/en/comparison-operators.html#operator_less-than) <br/>[`<=`](https://dev.mysql.com/doc/refman/8.0/en/comparison-operators.html#operator_less-than-or-equal) <br/>[`=`](https://dev.mysql.com/doc/refman/8.0/en/comparison-operators.html#operator_equal) <br/>[`!= (<>)`](https://dev.mysql.com/doc/refman/8.0/en/comparison-operators.html#operator_not-equal) <br/>[`>`](https://dev.mysql.com/doc/refman/8.0/en/comparison-operators.html#operator_greater-than) <br/>[`>=`](https://dev.mysql.com/doc/refman/8.0/en/comparison-operators.html#operator_greater-than-or-equal) <br/>[`<=>`](https://dev.mysql.com/doc/refman/8.0/en/comparison-operators.html#operator_equal-to) <br/>[BETWEEN ... AND ...](https://dev.mysql.com/doc/refman/8.0/en/comparison-operators.html#operator_between) <br/>[COALESCE()](https://dev.mysql.com/doc/refman/8.0/en/comparison-operators.html#function_coalesce) <br/>[IN()](https://dev.mysql.com/doc/refman/8.0/en/comparison-operators.html#operator_in) <br/>[INTERVAL()](https://dev.mysql.com/doc/refman/8.0/en/comparison-operators.html#function_interval) <br/>[IS NOT NULL](https://dev.mysql.com/doc/refman/8.0/en/comparison-operators.html#operator_is-not-null) <br/>[IS NOT](https://dev.mysql.com/doc/refman/8.0/en/comparison-operators.html#operator_is-not) <br/>[IS NULL](https://dev.mysql.com/doc/refman/8.0/en/comparison-operators.html#operator_is-null) <br/>[IS](https://dev.mysql.com/doc/refman/8.0/en/comparison-operators.html#operator_is) <br/>[ISNULL()](https://dev.mysql.com/doc/refman/8.0/en/comparison-operators.html#function_isnull) <br/>[LIKE](https://dev.mysql.com/doc/refman/8.0/en/string-comparison-functions.html#operator_like) <br/>[NOT BETWEEN ... AND ...](https://dev.mysql.com/doc/refman/8.0/en/comparison-operators.html#operator_not-between) <br/>[NOT IN()](https://dev.mysql.com/doc/refman/8.0/en/comparison-operators.html#operator_not-in) <br/>[NOT LIKE](https://dev.mysql.com/doc/refman/8.0/en/string-comparison-functions.html#operator_not-like) <br/>[STRCMP()](https://dev.mysql.com/doc/refman/8.0/en/string-comparison-functions.html#function_strcmp) |
| [Numeric functions and operators](/functions-and-operators/numeric-functions-and-operators.md) | [+](https://dev.mysql.com/doc/refman/8.0/en/arithmetic-functions.html#operator_plus) <br/>[-](https://dev.mysql.com/doc/refman/8.0/en/arithmetic-functions.html#operator_minus) <br/>[*](https://dev.mysql.com/doc/refman/8.0/en/arithmetic-functions.html#operator_times) <br/>[/](https://dev.mysql.com/doc/refman/8.0/en/arithmetic-functions.html#operator_divide) <br/>[DIV](https://dev.mysql.com/doc/refman/8.0/en/arithmetic-functions.html#operator_div) <br/>[% (MOD)](https://dev.mysql.com/doc/refman/8.0/en/arithmetic-functions.html#operator_mod) <br/>[-](https://dev.mysql.com/doc/refman/8.0/en/arithmetic-functions.html#operator_unary-minus) <br/>[ABS()](https://dev.mysql.com/doc/refman/8.0/en/mathematical-functions.html#function_abs) <br/>[ACOS()](https://dev.mysql.com/doc/refman/8.0/en/mathematical-functions.html#function_acos) <br/>[ASIN()](https://dev.mysql.com/doc/refman/8.0/en/mathematical-functions.html#function_asin) <br/>[ATAN()](https://dev.mysql.com/doc/refman/8.0/en/mathematical-functions.html#function_atan) <br/>[ATAN2() <br/>ATAN()](https://dev.mysql.com/doc/refman/8.0/en/mathematical-functions.html#function_atan2) <br/>[CEIL()](https://dev.mysql.com/doc/refman/8.0/en/mathematical-functions.html#function_ceil) <br/>[CEILING()](https://dev.mysql.com/doc/refman/8.0/en/mathematical-functions.html#function_ceiling) <br/>[CONV()](https://dev.mysql.com/doc/refman/8.0/en/mathematical-functions.html#function_conv) <br/>[COS()](https://dev.mysql.com/doc/refman/8.0/en/mathematical-functions.html#function_cos) <br/>[COT()](https://dev.mysql.com/doc/refman/8.0/en/mathematical-functions.html#function_cot) <br/>[CRC32()](https://dev.mysql.com/doc/refman/8.0/en/mathematical-functions.html#function_crc32) <br/>[DEGREES()](https://dev.mysql.com/doc/refman/8.0/en/mathematical-functions.html#function_degrees) <br/>[EXP()](https://dev.mysql.com/doc/refman/8.0/en/mathematical-functions.html#function_exp) <br/>[FLOOR()](https://dev.mysql.com/doc/refman/8.0/en/mathematical-functions.html#function_floor) <br/>[LN()](https://dev.mysql.com/doc/refman/8.0/en/mathematical-functions.html#function_ln) <br/>[LOG()](https://dev.mysql.com/doc/refman/8.0/en/mathematical-functions.html#function_log) <br/>[LOG10()](https://dev.mysql.com/doc/refman/8.0/en/mathematical-functions.html#function_log10) <br/>[LOG2()](https://dev.mysql.com/doc/refman/8.0/en/mathematical-functions.html#function_log2) <br/>[MOD()](https://dev.mysql.com/doc/refman/8.0/en/mathematical-functions.html#function_mod) <br/>[PI()](https://dev.mysql.com/doc/refman/8.0/en/mathematical-functions.html#function_pi) <br/>[POW()](https://dev.mysql.com/doc/refman/8.0/en/mathematical-functions.html#function_pow) <br/>[POWER()](https://dev.mysql.com/doc/refman/8.0/en/mathematical-functions.html#function_power) <br/>[RADIANS()](https://dev.mysql.com/doc/refman/8.0/en/mathematical-functions.html#function_radians) <br/>[RAND()](https://dev.mysql.com/doc/refman/8.0/en/mathematical-functions.html#function_rand) <br/>[ROUND()](https://dev.mysql.com/doc/refman/8.0/en/mathematical-functions.html#function_round) <br/>[SIGN()](https://dev.mysql.com/doc/refman/8.0/en/mathematical-functions.html#function_sign) <br/>[SIN()](https://dev.mysql.com/doc/refman/8.0/en/mathematical-functions.html#function_sin) <br/>[SQRT()](https://dev.mysql.com/doc/refman/8.0/en/mathematical-functions.html#function_sqrt) |
| [Control flow functions](/functions-and-operators/control-flow-functions.md) | [CASE](https://dev.mysql.com/doc/refman/8.0/en/flow-control-functions.html#operator_case) <br/>[IF()](https://dev.mysql.com/doc/refman/8.0/en/flow-control-functions.html#function_if) <br/>[IFNULL()](https://dev.mysql.com/doc/refman/8.0/en/flow-control-functions.html#function_ifnull) |
| [JSON functions](/functions-and-operators/json-functions.md) | [JSON_ARRAY_APPEND()](/functions-and-operators/json-functions/json-functions-modify.md#json_array_append) <br/>[JSON_ARRAY()](/functions-and-operators/json-functions/json-functions-create.md#json_array) <br/>[JSON_CONTAINS()](/functions-and-operators/json-functions/json-functions-search.md#json_contains) <br/>[JSON_EXTRACT()](/functions-and-operators/json-functions/json-functions-search.md#json_extract) <br/>[JSON_INSERT()](/functions-and-operators/json-functions/json-functions-modify.md#json_insert) <br/>[JSON_LENGTH()](/functions-and-operators/json-functions/json-functions-return.md#json_length) <br/>[JSON_MERGE_PATCH()](/functions-and-operators/json-functions/json-functions-modify.md#json_merge_patch) <br/>[JSON_MERGE()](/functions-and-operators/json-functions/json-functions-modify.md#json_merge) <br/>[JSON_OBJECT()](/functions-and-operators/json-functions/json-functions-create.md#json_object) <br/>[JSON_REMOVE()](/functions-and-operators/json-functions/json-functions-modify.md#json_remove) <br/>[JSON_REPLACE()](/functions-and-operators/json-functions/json-functions-modify.md#json_replace) <br/>[JSON_SET()](/functions-and-operators/json-functions/json-functions-modify.md#json_set) <br/>[JSON_TYPE()](/functions-and-operators/json-functions/json-functions-return.md#json_type) <br/>[JSON_UNQUOTE()](/functions-and-operators/json-functions/json-functions-modify.md#json_unquote) <br/>[JSON_VALID()](/functions-and-operators/json-functions/json-functions-return.md#json_valid) <br/>[MEMBER OF()](/functions-and-operators/json-functions/json-functions-search.md#member-of) |
| [Date and time functions](/functions-and-operators/date-and-time-functions.md) | [DATE()](https://dev.mysql.com/doc/refman/8.0/en/date-and-time-functions.html#function_date) <br/>[DATE_FORMAT()](https://dev.mysql.com/doc/refman/8.0/en/date-and-time-functions.html#function_date-format) <br/>[DATEDIFF()](https://dev.mysql.com/doc/refman/8.0/en/date-and-time-functions.html#function_datediff) <br/>[DAYOFMONTH()](https://dev.mysql.com/doc/refman/8.0/en/date-and-time-functions.html#function_dayofmonth) <br/>[DAYOFWEEK()](https://dev.mysql.com/doc/refman/8.0/en/date-and-time-functions.html#function_dayofweek) <br/>[DAYOFYEAR()](https://dev.mysql.com/doc/refman/8.0/en/date-and-time-functions.html#function_dayofyear) <br/>[FROM_DAYS()](https://dev.mysql.com/doc/refman/8.0/en/date-and-time-functions.html#function_from-days) <br/>[HOUR()](https://dev.mysql.com/doc/refman/8.0/en/date-and-time-functions.html#function_hour) <br/>[MAKEDATE()](https://dev.mysql.com/doc/refman/8.0/en/date-and-time-functions.html#function_makedate) <br/>[MAKETIME()](https://dev.mysql.com/doc/refman/8.0/en/date-and-time-functions.html#function_maketime) <br/>[MICROSECOND()](https://dev.mysql.com/doc/refman/8.0/en/date-and-time-functions.html#function_microsecond) <br/>[MINUTE()](https://dev.mysql.com/doc/refman/8.0/en/date-and-time-functions.html#function_minute) <br/>[MONTH()](https://dev.mysql.com/doc/refman/8.0/en/date-and-time-functions.html#function_month) <br/>[MONTHNAME()](https://dev.mysql.com/doc/refman/8.0/en/date-and-time-functions.html#function_monthname) <br/>[PERIOD_ADD()](https://dev.mysql.com/doc/refman/8.0/en/date-and-time-functions.html#function_period-add) <br/>[PERIOD_DIFF()](https://dev.mysql.com/doc/refman/8.0/en/date-and-time-functions.html#function_period-diff) <br/>[SEC_TO_TIME()](https://dev.mysql.com/doc/refman/8.0/en/date-and-time-functions.html#function_sec-to-time) <br/>[SECOND()](https://dev.mysql.com/doc/refman/8.0/en/date-and-time-functions.html#function_second) <br/>[SYSDATE()](https://dev.mysql.com/doc/refman/8.0/en/date-and-time-functions.html#function_sysdate) <br/>[TIME_TO_SEC()](https://dev.mysql.com/doc/refman/8.0/en/date-and-time-functions.html#function_time-to-sec) <br/>[TIMEDIFF()](https://dev.mysql.com/doc/refman/8.0/en/date-and-time-functions.html#function_timediff) <br/>[WEEK()](https://dev.mysql.com/doc/refman/8.0/en/date-and-time-functions.html#function_week) <br/>[WEEKOFYEAR()](https://dev.mysql.com/doc/refman/8.0/en/date-and-time-functions.html#function_weekofyear) <br/>[YEAR()](https://dev.mysql.com/doc/refman/8.0/en/date-and-time-functions.html#function_year) <br/>[DATE_ADD()](https://dev.mysql.com/doc/refman/8.0/en/date-and-time-functions.html#function_date-add) <br/>[DATE_SUB()](https://dev.mysql.com/doc/refman/8.0/en/date-and-time-functions.html#function_date-sub) <br/>[ADDDATE()](https://dev.mysql.com/doc/refman/8.0/en/date-and-time-functions.html#function_adddate) <br/>[SUBDATE()](https://dev.mysql.com/doc/refman/8.0/en/date-and-time-functions.html#function_subdate) |
| [String functions](/functions-and-operators/string-functions.md) | [ASCII()](/functions-and-operators/string-functions.md#ascii) <br/>[BIT_LENGTH()](/functions-and-operators/string-functions.md#bit_length) <br/>[CHAR()](/functions-and-operators/string-functions.md#char) <br/>[CHAR_LENGTH()](/functions-and-operators/string-functions.md#char_length) <br/>[CONCAT()](/functions-and-operators/string-functions.md#concat) <br/>[CONCAT_WS()](/functions-and-operators/string-functions.md#concat_ws) <br/>[ELT()](/functions-and-operators/string-functions.md#elt) <br/>[FIELD()](/functions-and-operators/string-functions.md#field) <br/>[HEX()](/functions-and-operators/string-functions.md#hex) <br/>[LENGTH()](/functions-and-operators/string-functions.md#length) <br/>[LIKE](/functions-and-operators/string-functions.md#like) <br/>[LOWER()](/functions-and-operators/string-functions.md#lower) <br/>[LTRIM()](/functions-and-operators/string-functions.md#ltrim) <br/>[MID()](/functions-and-operators/string-functions.md#mid) <br/>[NOT LIKE](/functions-and-operators/string-functions.md#not-like) <br/>[NOT REGEXP](/functions-and-operators/string-functions.md#not-regexp) <br/>[REGEXP](/functions-and-operators/string-functions.md#regexp) <br/>[REGEXP_LIKE()](/functions-and-operators/string-functions.md#regexp_like) <br/>[REGEXP_REPLACE()](/functions-and-operators/string-functions.md#regexp_replace) <br/>[REGEXP_SUBSTR()](/functions-and-operators/string-functions.md#regexp_substr) <br/>[REPLACE()](/functions-and-operators/string-functions.md#replace) <br/>[REVERSE()](/functions-and-operators/string-functions.md#reverse) <br/>[RIGHT()](/functions-and-operators/string-functions.md#right), [RLIKE](/functions-and-operators/string-functions.md#rlike) <br/>[RTRIM()](/functions-and-operators/string-functions.md#rtrim) <br/>[SPACE()](/functions-and-operators/string-functions.md#space) <br/>[STRCMP()](/functions-and-operators/string-functions.md#strcmp) <br/>[SUBSTR()](/functions-and-operators/string-functions.md#substr) <br/>[SUBSTRING()](/functions-and-operators/string-functions.md#substring) <br/>[UPPER()](/functions-and-operators/string-functions.md#upper) |
| [Aggregation functions](/functions-and-operators/aggregate-group-by-functions.md#aggregate-group-by-functions) | [COUNT()](https://dev.mysql.com/doc/refman/8.0/en/aggregate-functions.html#function_count) <br/>[COUNT(DISTINCT)](https://dev.mysql.com/doc/refman/8.0/en/aggregate-functions.html#function_count-distinct) <br/>[SUM()](https://dev.mysql.com/doc/refman/8.0/en/aggregate-functions.html#function_sum) <br/>[AVG()](https://dev.mysql.com/doc/refman/8.0/en/aggregate-functions.html#function_avg) <br/>[MAX()](https://dev.mysql.com/doc/refman/8.0/en/aggregate-functions.html#function_max) <br/>[MIN()](https://dev.mysql.com/doc/refman/8.0/en/aggregate-functions.html#function_min) <br/>[VARIANCE()](https://dev.mysql.com/doc/refman/8.0/en/aggregate-functions.html#function_variance) <br/>[VAR_POP()](https://dev.mysql.com/doc/refman/8.0/en/aggregate-functions.html#function_var-pop) <br/>[STD()](https://dev.mysql.com/doc/refman/8.0/en/aggregate-functions.html#function_std) <br/>[STDDEV()](https://dev.mysql.com/doc/refman/8.0/en/aggregate-functions.html#function_stddev) <br/>[STDDEV_POP](https://dev.mysql.com/doc/refman/8.0/en/aggregate-functions.html#function_stddev-pop) <br/>[VAR_SAMP()](https://dev.mysql.com/doc/refman/8.0/en/aggregate-functions.html#function_var-samp) <br/>[STDDEV_SAMP()](https://dev.mysql.com/doc/refman/8.0/en/aggregate-functions.html#function_stddev-samp) <br/>[JSON_ARRAYAGG(key)](https://dev.mysql.com/doc/refman/8.0/en/aggregate-functions.html#function_json-arrayagg) <br/>[JSON_OBJECTAGG(key, value)](https://dev.mysql.com/doc/refman/8.0/en/aggregate-functions.html#function_json-objectagg) |
| [Encryption and compression functions](/functions-and-operators/encryption-and-compression-functions.md#encryption-and-compression-functions) | [MD5()](/functions-and-operators/encryption-and-compression-functions.md#md5) <br/>[SHA1(), SHA()](/functions-and-operators/encryption-and-compression-functions.md#sha1) <br/>[UNCOMPRESSED_LENGTH()](/functions-and-operators/encryption-and-compression-functions.md#uncompressed_length) |
| [Cast functions and operators](/functions-and-operators/cast-functions-and-operators.md#cast-functions-and-operators) | [CAST()](/functions-and-operators/cast-functions-and-operators.md#cast) <br/>[CONVERT()](/functions-and-operators/cast-functions-and-operators.md#convert) |
| [Miscellaneous functions](/functions-and-operators/miscellaneous-functions.md#supported-functions) | [UUID()](/functions-and-operators/miscellaneous-functions.md#uuid) |
| [Window Functions](/functions-and-operators/window-functions.md) | None |

## Blocklist specific expressions

If unexpected behavior occurs in the calculation process when pushing down the [supported expressions](#supported-expressions-for-pushdown-to-tikv) or specific data types (**only** the [`ENUM` type](/data-type-string.md#enum-type) and the [`BIT` type](/data-type-numeric.md#bit-type)), you can restore the application quickly by prohibiting the pushdown of the corresponding functions, operators, or data types. Specifically, you can prohibit the functions, operators, or data types from being pushed down by adding them to the blocklist `mysql.expr_pushdown_blacklist`. For details, refer to [Add to the blocklist](/blocklist-control-plan.md#disable-the-pushdown-of-specific-expressions).
