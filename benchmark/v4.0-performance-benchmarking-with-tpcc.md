---
title: TiDB TPC-C Performance Test Report -- v4.0 vs. v3.0
summary: Compare the TPC-C performance of TiDB 4.0 and TiDB 3.0 using BenchmarkSQL.
---

# TiDB TPC-C Performance Test Report -- v4.0 vs. v3.0

## Test purpose

This test aims to compare the TPC-C performance of TiDB 4.0 and TiDB 3.0 in the Online Transactional Processing (OLTP) scenario.

## Test environment (AWS EC2)

### Hardware configuration

| Service type   | EC2 type   |    Instance count  |
|:----------|:----------|:----------|
| PD        | m5.xlarge |     3     |
| TiKV      | i3.4xlarge|     3     |
| TiDB      | c5.4xlarge|     3     |
| TPC-C     | m5.4xlarge|     1     |

### Software version

| Service type   | Software version    |
|:----------|:-----------|
| PD        | 3.0 and 4.0   |
| TiDB      | 3.0 and 4.0   |
| TiKV      | 3.0 and 4.0   |
| BenchmarkSQL  | None     |

### Parameter configuration

#### TiDB v3.0 configuration

{{< copyable "" >}}

```yaml
log.level: "error"
performance.max-procs: 20
prepared-plan-cache.enabled: true
tikv-client.max-batch-wait-time: 2000000
```

#### TiKV v3.0 configuration

{{< copyable "" >}}

```yaml
storage.scheduler-worker-pool-size: 5
raftstore.store-pool-size: 3
raftstore.apply-pool-size: 3
rocksdb.max-background-jobs: 3
raftdb.max-background-jobs: 3
raftdb.allow-concurrent-memtable-write: true
server.grpc-concurrency: 6
readpool.storage.normal-concurrency: 10
readpool.coprocessor.normal-concurrency: 5
```

#### TiDB v4.0 configuration

{{< copyable "" >}}

```yaml
log.level: "error"
performance.max-procs: 20
prepared-plan-cache.enabled: true
tikv-client.max-batch-wait-time: 2000000
```

#### TiKV v4.0 configuration

{{< copyable "" >}}

```yaml
storage.scheduler-worker-pool-size: 5
raftstore.store-pool-size: 3
raftstore.apply-pool-size: 3
rocksdb.max-background-jobs: 3
raftdb.max-background-jobs: 3
raftdb.allow-concurrent-memtable-write: true
server.grpc-concurrency: 6
readpool.unified.min-thread-count: 5
readpool.unified.max-thread-count: 20
readpool.storage.normal-concurrency: 10
pessimistic-txn.pipelined: true
```

#### Global variable configuration

{{< copyable "sql" >}}

```sql
set global tidb_hashagg_final_concurrency=1;
set global tidb_hashagg_partial_concurrency=1;
set global tidb_disable_txn_auto_retry=0;
```

## Test plan

1. Deploy TiDB v4.0 and v3.0 using TiUP.

2. Use BenchmarkSQL to import the TPC-C 5000 Warehouse data.

    1. Compile BenchmarkSQL:

        {{< copyable "bash" >}}

        ```bash
        git clone https://github.com/pingcap/benchmarksql && cd benchmarksql && ant
        ```

    2. Enter the `run` directory, edit the `props.mysql` file according to the actual situation, and modify the `conn`, `warehouses`, `loadWorkers`, `terminals`, and `runMins` configuration items.

    3. Execute the `runSQL.sh ./props.mysql sql.mysql/tableCreates.sql` command.

    4. Execute the `runSQL.sh ./props.mysql sql.mysql/indexCreates.sql` command.

    5. Run MySQL client and execute the `analyze table` statement on every table.

3. Execute the `runBenchmark.sh ./props.mysql` command.

4. Extract the tpmC data of New Order from the result.

## Test result

According to the test statistics, the TPC-C performance of TiDB v4.0 has **increased by 50%** compared with that of TiDB v3.0.

![TPC-C](/media/tpcc-v4vsv3.png)
