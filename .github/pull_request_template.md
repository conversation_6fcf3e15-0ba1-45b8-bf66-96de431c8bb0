<!--Thanks for your contribution to TiDB documentation. Please answer the following questions.-->
<!--
We provide several doc templates for you to use to create documentation that aligns with our style.
Please check out these templates before you submit the pull request:
https://github.com/pingcap/docs/tree/master/resources/doc-templates
-->

### First-time contributors' checklist <!--Remove this section if you're not a first-time contributor.-->

- [ ] I've signed the [**Contributor License Agreement**](https://cla.pingcap.net/pingcap/docs), which is required for the repository owners to accept my contribution.

### What is changed, added or deleted? (Required)

<!--Tell us what you did and why.-->

### Which TiDB version(s) do your changes apply to? (Required)

<!-- Fill in "x" in [] to tick the checkbox below.-->

**Tips for choosing the affected version(s):**

By default, **CHOOSE MASTER ONLY** so your changes will be applied to the next TiDB major or minor releases. If your PR involves a product feature behavior change or a compatibility change, **CHOOSE THE AFFECTED RELEASE BRANCH(ES) AND MASTER**.

For details, see [tips for choosing the affected versions](https://github.com/pingcap/docs/blob/master/CONTRIBUTING.md#guideline-for-choosing-the-affected-versions).

- [ ] master (the latest development version)
- [ ] v8.5 (TiDB 8.5 versions)
- [ ] v8.4 (TiDB 8.4 versions)
- [ ] v8.3 (TiDB 8.3 versions)
- [ ] v8.2 (TiDB 8.2 versions)
- [ ] v8.1 (TiDB 8.1 versions)
- [ ] v7.5 (TiDB 7.5 versions)
- [ ] v7.1 (TiDB 7.1 versions)
- [ ] v6.5 (TiDB 6.5 versions)
- [ ] v6.1 (TiDB 6.1 versions)
- [ ] v5.4 (TiDB 5.4 versions)
- [ ] v5.3 (TiDB 5.3 versions)

### What is the related PR or file link(s)?

<!--Reference link(s) will help reviewers review your PR quickly.-->

- This PR is translated from:
- Other reference link(s):

### Do your changes match any of the following descriptions?

- [ ] Delete files
- [ ] Change aliases
- [ ] Need modification after applied to another branch <!-- If yes, please comment "/label version-specific-changes-required" below to trigger the bot to add the label. -->
- [ ] Might cause conflicts after applied to another branch
