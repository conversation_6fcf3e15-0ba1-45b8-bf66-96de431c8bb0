name: Trigger docs site update

on:
  push:
    paths-ignore:
      - ".github/**"
    branches:
      - master
      - release-8.5
      - release-7.2
      - release-7.1
      - release-7.0
      - release-6.5
      - release-6.1
      - release-5.4
      - release-5.3
      - release-5.2
      - release-5.1
      - release-5.0
      - release-4.0
      - release-3.0

jobs:
  trigger:
    runs-on: ubuntu-latest

    steps:
      - name: Extract sha
        id: extract
        shell: bash
        run: |
          echo "sha=$(sha=${{ github.sha }}; echo ${sha:0:6})" >> $GITHUB_OUTPUT

      - name: trigger docs-staging workflow
        run: |
          curl \
          -X POST \
          -H "Accept: application/vnd.github+json" \
          -H "Authorization: token ${{ secrets.DOCS_STAGING }}" \
          https://api.github.com/repos/pingcap/docs-staging/actions/workflows/update.yml/dispatches \
          -d '{"ref":"main","inputs":{"full": "false", "repo":"${{ github.repository }}","branch":"${{ github.ref_name }}"}}'
