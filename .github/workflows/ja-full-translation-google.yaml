name: JA Full Translation (Google version)

on: workflow_dispatch

jobs:
  ja-translation:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        name: Download translator repo
        with:
          repository: "shczhen/markdown-translator"
          path: "markdown-translator"
      - uses: actions/checkout@v4
        name: Download specified branch of docs repo
        with:
          ref: "release-8.1"
          path: "docs"
      - uses: actions/setup-node@v4
        name: Setup node 18
        with:
          node-version: 18

      - run: |
          sudo apt install tree -y

      - name: Copy doc files to translator folder
        run: |
          cp -r docs markdown-translator/markdowns
          rm -rf markdown-translator/markdowns/.github
          rm -rf markdown-translator/markdowns/scripts
          rm -rf markdown-translator/markdowns/media

      - name: Config and translate
        run: |
          cd markdown-translator
          echo ${{secrets.GCP_KEY}} | base64 --decode >> key.json
          export GOOGLE_APPLICATION_CREDENTIALS=key.json
          export PROJECT_ID=${{ secrets.GCP_PROJECT_ID }}
          export GLOSSARY_ID=${{ secrets.GCP_GLOSSARY_ID }}
          yarn
          node src/index.js
          cd ..
      - name: <PERSON><PERSON> translated files to docs repo
        run: |
          cd docs
          git checkout -b i18n-ja-release-8.1
          cd ..
          cp -r markdown-translator/output/markdowns/* docs/

      - name: Git commit and push
        run: |
          cd docs
          git config user.name github-actions
          git config user.email <EMAIL>
          git add .
          if git status | grep -q "Changes to be committed"
          then
            git commit -m "Update full translated files for JA"
          else
            echo "No changes detected, skipped"
          fi
          git push -f --set-upstream origin i18n-ja-release-8.1

  dispatch:
    runs-on: ubuntu-latest
    needs: [ja-translation]

    steps:
      - name: trigger docs-staging workflow
        run: |
          curl \
          -X POST \
          -H "Accept: application/vnd.github+json" \
          -H "Authorization: token ${{ secrets.DOCS_STAGING }}" \
          https://api.github.com/repos/pingcap/docs-staging/actions/workflows/update.yml/dispatches \
          -d '{"ref":"main","inputs":{"full": "false", "repo":"${{ github.repository }}","branch":"i18n-ja-release-8.1"}}'
