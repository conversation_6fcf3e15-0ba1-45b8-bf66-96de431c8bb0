name: ci

on: [pull_request]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}-${{ github.event_name }}
  cancel-in-progress: true

jobs:
  tidb-check:
    runs-on: ubuntu-latest
    steps:
      - name: Check out
        uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: "18"
      - name: Verify duplicated file names
        run: ./scripts/verify-duplicated-file-name.sh
      - name: Verify internal links and anchors - tidb only
        run: |
          npm i
          node ./scripts/filterNonCloudDoc.js
          cp -r ./scripts ./tmp
          cp -r ./media ./tmp
          cp .gitignore ./tmp/
          cd ./tmp
          ./scripts/verify-links.sh
          ./scripts/verify-link-anchors.sh

  tidb-cloud-check:
    runs-on: ubuntu-latest
    steps:
      - name: Check out
        uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: "18"
      - name: Check TOC-tidb-cloud.md existence
        id: check_cloud_toc
        uses: andstor/file-existence-action@v2
        with:
          files: "TOC-tidb-cloud.md"
      - name: Verify internal links - cloud only
        if: steps.check_cloud_toc.outputs.files_exists == 'true'
        run: |
          npm i
          node ./scripts/filterCloudDoc.js
          cp -r ./scripts ./tmp
          cp -r ./media ./tmp
          cp .gitignore ./tmp/
          cd ./tmp
          ./scripts/verify-links.sh
          ./scripts/verify-link-anchors.sh

  vale:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Vale Linter
        uses: errata-ai/vale-action@reviewdog
        with:
          # Optional
          # Specify file path to lint
          #files: .

          # Optional
          filter_mode: added
        env:
          # Required, set by GitHub actions automatically:
          # https://docs.github.com/en/actions/security-guides/automatic-token-authentication#about-the-github_token-secret
          GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}
