name: cron

on:
  schedule:
    - cron: "0 9 * * 3"
  workflow_dispatch:

jobs:
  ja:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        name: Download translator repo
        with:
          repository: "shczhen/markdown-translator"
          path: "markdown-translator"
      - uses: actions/checkout@v4
        name: Download docs repo and specified branch
        with:
          ref: "i18n-ja-release-8.1"
          path: "docs"
      - uses: actions/setup-node@v4
        name: Setup node 18
        with:
          node-version: 18

      - run: |
          sudo apt install tree -y

      - name: Download files by comparing commits
        run: |
          export GH_TOKEN=${{github.token}}
          cd docs
          npm i
          node scripts/filterUpdateFiles.js
          tree tmp
          cd ..
      - name: Copy new files to translator folder
        run: |
          cp -r docs/tmp markdown-translator/markdowns
      - name: Config and translate
        run: |
          cd markdown-translator
          echo ${{secrets.GCP_KEY}} | base64 --decode >> key.json
          export GOOGLE_APPLICATION_CREDENTIALS=key.json
          export PROJECT_ID=${{ secrets.GCP_PROJECT_ID }}
          export GLOSSARY_ID=${{ secrets.GCP_GLOSSARY_ID }}
          yarn
          node src/index.js
          cd ..
      - name: Copy translated files to docs repo
        run: |
          cp -r markdown-translator/output/markdowns/* docs/

      - name: Git commit and push
        run: |
          cd docs
          git status
          git config user.name github-actions
          git config user.email <EMAIL>
          git add .
          git commit -m "update translated files"
          git push

# When ja-kernal version is different with cloud, open the comment and run the github action!
#   ja-cloud:
#     runs-on: ubuntu-latest

#     steps:
#       - uses: actions/checkout@v4
#         name: Download translator repo
#         with:
#           repository: "shczhen/markdown-translator"
#           path: "markdown-translator"
#       - uses: actions/checkout@v4
#         name: Download docs repo and specified branch
#         with:
#           ref: "i18n-ja-release-7.1"
#           path: "docs"
#       - uses: actions/setup-node@v4
#         name: Setup node 18
#         with:
#           node-version: 18

#       - run: |
#           sudo apt install tree -y

#       - name: Download files by comparing commits
#         run: |
#           export GH_TOKEN=${{github.token}}
#           cd docs
#           npm i
#           node scripts/filterUpdateFiles.js
#           tree tmp
#           cd ..
#       - name: Copy new files to translator folder
#         run: |
#           cp -r docs/tmp markdown-translator/markdowns
#       - name: Config and translate
#         run: |
#           cd markdown-translator
#           echo ${{secrets.GCP_KEY}} | base64 --decode >> key.json
#           export GOOGLE_APPLICATION_CREDENTIALS=key.json
#           export PROJECT_ID=${{ secrets.GCP_PROJECT_ID }}
#           export GLOSSARY_ID=${{ secrets.GCP_GLOSSARY_ID }}
#           yarn
#           node src/index.js
#           cd ..
#       - name: Copy translated files to docs repo
#         run: |
#           cp -r markdown-translator/output/markdowns/* docs/

#       - name: Git commit and push
#         run: |
#           cd docs
#           git status
#           git config user.name github-actions
#           git config user.email <EMAIL>
#           git add .
#           git commit -m "update translated files"
#           git push
  dispatch:
    runs-on: ubuntu-latest
    needs: [ja]

    steps:
      - name: trigger docs-staging workflow
        run: |
          curl \
          -X POST \
          -H "Accept: application/vnd.github+json" \
          -H "Authorization: token ${{ secrets.DOCS_STAGING }}" \
          https://api.github.com/repos/pingcap/docs-staging/actions/workflows/update.yml/dispatches \
          -d '{"ref":"main","inputs":{"full": "false", "repo":"${{ github.repository }}","branch":"i18n-ja-release-8.1"}}'
