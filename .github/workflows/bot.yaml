name: bot

on:
  workflow_dispatch:
  schedule:
    - cron: "30 1 * * 1-5"

jobs:
  pr_reminder:
    runs-on: ubuntu-latest
    steps:
      - name: Run PR reminder
        env:
          WEBHOOK: ${{secrets.BOT_WEBHOOK_URL}}
        run: |
          wget https://raw.githubusercontent.com/pingcap/docs/master/scripts/pr_reminder.py;
          pip3 install lxml;
          python3 pr_reminder.py "$WEBHOOK"
      - name: Run PR reminder by assignee
        env:
          WEBHOOK: ${{secrets.BOT_WEBHOOK_URL}}
        run: |
          wget https://raw.githubusercontent.com/pingcap/docs/master/scripts/pr_reminder_basedon_assignee.py;
          pip3 install lxml;
          python3 pr_reminder_basedon_assignee.py "$WEBHOOK"
