name: Upload media files to <PERSON><PERSON> when they change
on:
  push:
    branches:
      - master
      - release-*
    paths:
      - media/**
jobs:
  upload:
    name: Upload media files
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          # Must use at least depth 2!
          fetch-depth: 2

      - name: Download cloud_assets_utils
        run: |
          curl -L https://github.com/pingcap/cloud-assets-utils/releases/download/v0.2.0/cloud_assets_utils-ubuntu-latest -o cloud_assets_utils
          chmod +x cloud_assets_utils
          sudo mv cloud_assets_utils /usr/local/bin/cloud-assets-utils
      - name: Configure qshell
        run: |
          curl http://devtools.qiniu.com/qshell-linux-x64-v2.4.1.zip -o qshell.zip
          unzip qshell.zip
          sudo mv qshell-linux-x64-v2.4.1 /usr/local/bin/qshell
          qshell account ${{ secrets.QINIU_ACCESS_KEY }} ${{ secrets.QINIU_SECRET_KEY }} test
      #- name: Configure awscli
      #  run: |
      #    pip3 install --upgrade setuptools
      #    pip3 install awscli
      #    printf "%s\n" ${{ secrets.AWS_ACCESS_KEY }} ${{ secrets.AWS_SECRET_KEY }} ${{ secrets.AWS_REGION }} "json" | aws configure
      - name: Upload
        run: cloud-assets-utils verify-and-sync -qiniu true -qiniu-bucket ${{ secrets.QINIU_BUCKET_NAME }} media -replace-first-path-to images/docs -cdn-refresh https://download.pingcap.com/

      - name: Install coscli
        run: |
          wget https://cosbrowser.cloud.tencent.com/software/coscli/coscli-linux-amd64
          mv coscli-linux-amd64 coscli
          chmod 755 coscli

      - name: Upload to COS
        run: |
          ./coscli sync media/ cos://${{ secrets.TENCENTCLOUD_BUCKET_ID }}/media/images/docs \
            --init-skip \
            --recursive \
            --routines 16 \
            --secret-id ${{ secrets.TENCENTCLOUD_SECRET_ID }} \
            --secret-key ${{ secrets.TENCENTCLOUD_SECRET_KEY }} \
            --endpoint cos.ap-beijing.myqcloud.com

  cdn-refresh:
    needs: upload
    runs-on: ubuntu-latest
    name: Refresh CDN Cache
    env:
      TENCENTCLOUD_SECRET_ID: ${{ secrets.TENCENTCLOUD_SECRET_ID }}
      TENCENTCLOUD_SECRET_KEY: ${{ secrets.TENCENTCLOUD_SECRET_KEY }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Python environment
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"
          architecture: "x64"

      - name: Install Tencent Cloud CLI
        run: pipx install tccli

      - name: Purge production CDN cache
        run: tccli cdn PurgePathCache --Paths '["https://docs-download.pingcap.com/media/images/docs/"]' --FlushType delete
