name: Automatic Rebase
on:
  issue_comment:
    types: [created]
jobs:
  rebase:
    name: Rebase
    if: github.event.issue.pull_request != '' && contains(github.event.comment.body, '/rebase')
    runs-on: ubuntu-latest
    steps:
      - name: Checkout the latest code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.REBASE_SECRET_KEY }}
          fetch-depth: 0 # otherwise, you will fail to push refs to dest repo
      - name: Automatic Rebase
        uses: cirrus-actions/rebase@1.6
        env:
          GITHUB_TOKEN: ${{ secrets.REBASE_SECRET_KEY }}