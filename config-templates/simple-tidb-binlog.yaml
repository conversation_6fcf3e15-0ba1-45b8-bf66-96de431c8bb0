# # Global variables are applied to all deployments and used as the default value of
# # the deployments if a specific deployment value is missing.
global:
  user: "tidb"
  ssh_port: 22
  deploy_dir: "/tidb-deploy"
  data_dir: "/tidb-data"

server_configs:
  tidb:
    binlog.enable: true
    binlog.ignore-error: true

pd_servers:
  - host: ********
  - host: ********
  - host: ********
tidb_servers:
  - host: ********
  - host: ********
  - host: ********
tikv_servers:
  - host: ********
  - host: ********
  - host: ********

pump_servers:
  - host: ********
  - host: ********
  - host: ********
drainer_servers:
  - host: ********2
    config:
      syncer.db-type: "tidb"
      syncer.to.host: "********2"
      syncer.to.user: "root"
      syncer.to.password: ""
      syncer.to.port: 4000

monitoring_servers:
  - host: *********

grafana_servers:
  - host: *********

alertmanager_servers:
  - host: *********