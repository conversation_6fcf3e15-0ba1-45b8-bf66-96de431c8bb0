# # Global variables are applied to all deployments and used as the default value of
# # the deployments if a specific deployment value is missing.
global:
  user: "tidb"
  ssh_port: 22
  deploy_dir: "/tidb-deploy"
  data_dir: "/tidb-data"

# # Monitored variables are applied to all the machines.
monitored:
  node_exporter_port: 9100
  blackbox_exporter_port: 9115
  # deploy_dir: "/tidb-deploy/monitored-9100"
  # data_dir: "/tidb-data/monitored-9100"
  # log_dir: "/tidb-deploy/monitored-9100/log"

# # Server configs are used to specify the runtime configuration of TiDB components.
# # All configuration items can be found in TiDB docs:
# # - TiDB: https://docs.pingcap.com/tidb/stable/tidb-configuration-file
# # - TiKV: https://docs.pingcap.com/tidb/stable/tikv-configuration-file
# # - PD: https://docs.pingcap.com/tidb/stable/pd-configuration-file
# # All configuration items use points to represent the hierarchy, e.g:
# #   readpool.storage.use-unified-pool
# #      
# # You can overwrite this configuration via the instance-level `config` field.

server_configs:
  tidb:
    log.slow-threshold: 300
    binlog.enable: true
    binlog.ignore-error: true
  tikv:
    # server.grpc-concurrency: 4
    # raftstore.apply-pool-size: 2
    # raftstore.store-pool-size: 2
    # rocksdb.max-sub-compactions: 1
    # storage.block-cache.capacity: "16GB"
    # readpool.unified.max-thread-count: 12
    readpool.storage.use-unified-pool: false
    readpool.coprocessor.use-unified-pool: true
  pd:
    schedule.leader-schedule-limit: 4
    schedule.region-schedule-limit: 2048
    schedule.replica-schedule-limit: 64

pd_servers:
  - host: ********
    # ssh_port: 22
    # name: "pd-1"
    # client_port: 2379
    # peer_port: 2380
    # deploy_dir: "/tidb-deploy/pd-2379"
    # data_dir: "/tidb-data/pd-2379"
    # log_dir: "/tidb-deploy/pd-2379/log"
    # numa_node: "0,1"
    # # The following configs are used to overwrite the `server_configs.pd` values.
    # config:
    #   schedule.max-merge-region-size: 20
    #   schedule.max-merge-region-keys: 200000
  - host: ********
  - host: ********
tidb_servers:
  - host: ********
    # ssh_port: 22
    # port: 4000
    # status_port: 10080
    # deploy_dir: "/tidb-deploy/tidb-4000"
    # log_dir: "/tidb-deploy/tidb-4000/log"
    # numa_node: "0,1"
    # # The following configs are used to overwrite the `server_configs.tidb` values.
    # config:
    #   log.slow-query-file: tidb-slow-overwrited.log
  - host: ********
  - host: ********
tikv_servers:
  - host: ********
    # ssh_port: 22
    # port: 20160
    # status_port: 20180
    # deploy_dir: "/tidb-deploy/tikv-20160"
    # data_dir: "/tidb-data/tikv-20160"
    # log_dir: "/tidb-deploy/tikv-20160/log"
    # numa_node: "0,1"
    # # The following configs are used to overwrite the `server_configs.tikv` values.
    # config:
    #   server.grpc-concurrency: 4
    #   server.labels: { zone: "zone1", dc: "dc1", host: "host1" }
  - host: ********
  - host: ********

pump_servers:
  - host: ********
    ssh_port: 22
    port: 8250
    deploy_dir: "/tidb-deploy/pump-8250"
    data_dir: "/tidb-data/pump-8250"
    # The following configs are used to overwrite the `server_configs.pump` values.
    config:
      gc: 7
  - host: ********
    ssh_port: 22
    port: 8250
    deploy_dir: "/tidb-deploy/pump-8250"
    data_dir: "/tidb-data/pump-8250"
    # The following configs are used to overwrite the `server_configs.pump` values.
    config:
      gc: 7
  - host: ********
    ssh_port: 22
    port: 8250
    deploy_dir: "/tidb-deploy/pump-8250"
    data_dir: "/tidb-data/pump-8250"
    # The following configs are used to overwrite the `server_configs.pump` values.
    config:
      gc: 7
drainer_servers:
  - host: ********2
    port: 8249
    deploy_dir: "/tidb-deploy/drainer-8249"
    data_dir: "/tidb-data/drainer-8249"
    # If drainer doesn't have a checkpoint, use initial commitTS as the initial checkpoint.
    # Will get a latest timestamp from pd if commit_ts is set to -1 (the default value).
    commit_ts: -1
    # The following configs are used to overwrite the `server_configs.drainer` values.
    config:
      syncer.db-type: "tidb"
      syncer.to.host: "********2"
      syncer.to.user: "root"
      syncer.to.password: ""
      syncer.to.port: 4000
      syncer.to.checkpoint:
        schema: "tidb_binlog"
        type: "tidb"
        host: "********4"
        user: "root"
        password: "123"
        port: 4000
  - host: ********3
    port: 8249
    deploy_dir: "/tidb-deploy/drainer-8249"
    data_dir: "/tidb-data/drainer-8249"
    # If Drainer does not have a checkpoint, use the initial commitTS as the initial checkpoint.
    # If commit_ts is set to -1 (the default value), you will get a latest timestamp from PD.
    commit_ts: -1
    # The following configurations are used to overwrite the `server_configs.drainer` values.
    config:
      syncer.db-type: "kafka"
      syncer.replicate-do-db:
      - db1
      - db2
      syncer.to.kafka-addrs: "********0:9092,********1:9092,********2:9092"
      syncer.to.kafka-version: "2.4.0"
      syncer.to.topic-name: "asyouwish"
         
monitoring_servers:
  - host: *********
    # ssh_port: 22
    # port: 9090
    # deploy_dir: "/tidb-deploy/prometheus-8249"
    # data_dir: "/tidb-data/prometheus-8249"
    # log_dir: "/tidb-deploy/prometheus-8249/log"

grafana_servers:
  - host: *********
    # port: 3000
    # deploy_dir: /tidb-deploy/grafana-3000

alertmanager_servers:
  - host: *********
    # ssh_port: 22
    # web_port: 9093
    # cluster_port: 9094
    # deploy_dir: "/tidb-deploy/alertmanager-9093"
    # data_dir: "/tidb-data/alertmanager-9093"
    # log_dir: "/tidb-deploy/alertmanager-9093/log"