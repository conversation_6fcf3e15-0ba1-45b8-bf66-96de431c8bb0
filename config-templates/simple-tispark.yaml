# # Global variables are applied to all deployments and used as the default value of
# # the deployments if a specific deployment value is missing.
global:
  user: "tidb"
  ssh_port: 22
  deploy_dir: "/tidb-deploy"
  data_dir: "/tidb-data"

pd_servers:
  - host: ********
  - host: ********
  - host: ********

tidb_servers:
  - host: ********
  - host: ********
  - host: ********

tikv_servers:
  - host: ********
  - host: ********
  - host: ********


# NOTE: TiSpark support is an experimental feature, it's not recommend to be used in
# production at present.
# To use TiSpark, you need to manually install Java Runtime Environment (JRE) 8 on the
# host, see the OpenJDK doc for a reference: https://openjdk.java.net/install/
# NOTE: Only 1 master node is supported for now
tispark_masters:
  - host: ********1

# NOTE: multiple worker nodes on the same host is not supported by Spark
tispark_workers:
  - host: ********2
  - host: ********3

monitoring_servers:
  - host: *********

grafana_servers:
  - host: *********

alertmanager_servers:
  - host: *********
