# # Global variables are applied to all deployments and used as the default value of
# # the deployments if a specific deployment value is missing.
global:
  user: "tidb"
  ssh_port: 22
  deploy_dir: "/tidb-deploy"
  data_dir: "/tidb-data"

server_configs:
  tidb:
    binlog.enable: true
    binlog.ignore-error: true

pd_servers:
  - host: ********
  - host: ********
  - host: ********
tidb_servers:
  - host: ********
  - host: ********
  - host: ********
tikv_servers:
  - host: ********
  - host: ********
  - host: ********

pump_servers:
  - host: ********
  - host: ********
  - host: ********
drainer_servers:
  - host: ********2
    # drainer meta data directory path
    data_dir: "/path/to/save/data"
    config:
      syncer.db-type: "file"
      # directory to save binlog file, default same as data-dir(save checkpoint file) if this is not configured.
      # syncer.to.dir: "/path/to/save/binlog"

monitoring_servers:
  - host: *********

grafana_servers:
  - host: *********

alertmanager_servers:
  - host: *********
