# # Global variables are applied to all deployments and used as the default value of
# # the deployments if a specific deployment value is missing.
global:
  user: "tidb"
  ssh_port: 22
  deploy_dir: "/tidb-deploy"
  data_dir: "/tidb-data"
component_versions:
  tiproxy: "v1.2.0"
server_configs:
  tidb:
    graceful-wait-before-shutdown: 30
  tiproxy:
    ha.virtual-ip: "*********/24"
    ha.interface: "eth0"
    graceful-wait-before-shutdown: 15

pd_servers:
  - host: ********
  - host: ********
  - host: ********

tidb_servers:
  - host: ********
  - host: ********
  - host: ********

tikv_servers:
  - host: ********
  - host: ********
  - host: ********

tiproxy_servers:
  - host: ********1
    deploy_dir: "/tiproxy-deploy"
    port: 6000
    status_port: 3080
    config:
      labels: { zone: "east" }
  - host: ********2
    deploy_dir: "/tiproxy-deploy"
    port: 6000
    status_port: 3080
    config:
      labels: { zone: "west" }

monitoring_servers:
  - host: *********

grafana_servers:
  - host: *********

alertmanager_servers:
  - host: *********
