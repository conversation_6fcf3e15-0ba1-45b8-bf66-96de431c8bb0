# Tip: PD priority needs to be manually set using the PD-ctl client tool. such as, member Leader_priority PD-name numbers.
# Global variables are applied to all deployments and used as the default value of
# the deployments if a specific deployment value is missing.
#
# Abbreviations used in this example:
# sh: Shanghai Zone
# bj: Beijing Zone
# sha: Shanghai Datacenter A
# bja: Beijing Datacenter A
# bjb: Beijing Datacenter B

global:
  user: "tidb"
  ssh_port: 22
  deploy_dir: "/tidb-deploy"
  data_dir: "/tidb-data"
monitored:
  node_exporter_port: 9100
  blackbox_exporter_port: 9115
  deploy_dir: "/tidb-deploy/monitored-9100"
server_configs:
  tidb:
    log.level: debug
    log.slow-query-file: tidb-slow.log
  tikv:
    server.grpc-compression-type: gzip
    readpool.storage.use-unified-pool: true
    readpool.storage.low-concurrency: 8
  pd:
    replication.location-labels: ["zone","dc","rack","host"]
    replication.max-replicas: 5
    label-property:  # Since TiDB 5.2, the `label-property` configuration is not supported by default. To set the replica policy, use the placement rules.
      reject-leader:
        - key: "dc"
          value: "sha"
pd_servers:
 - host: ********
 - host: ********
 - host: ********
 - host: ********
 - host: ********0
tidb_servers:
 - host: ********
 - host: ********
 - host: ********
 - host: ********
 - host: ********
tikv_servers:
 - host: ********* 
   ssh_port: 22
   port: 20160
   status_port: 20180
   deploy_dir: "/tidb-deploy/tikv-20160"
   data_dir: "/tidb-data/tikv-20160"
   config:
     server.labels:
       zone: bj
       dc: bja
       rack: rack1
       host: host1
 - host: ********2
   ssh_port: 22
   port: 20161
   status_port: 20181
   deploy_dir: "/tidb-deploy/tikv-20161"
   data_dir: "/tidb-data/tikv-20161"
   config:
     server.labels:
       zone: bj
       dc: bja
       rack: rack1
       host: host2
 - host: *********
   ssh_port: 22
   port: 20160
   status_port: 20180
   deploy_dir: "/tidb-deploy/tikv-20160"
   data_dir: "/tidb-data/tikv-20160"
   config:
     server.labels:
       zone: bj
       dc: bjb
       rack: rack1
       host: host1
 - host: *********
   ssh_port: 22
   port: 20161
   status_port: 20181
   deploy_dir: "/tidb-deploy/tikv-20161"
   data_dir: "/tidb-data/tikv-20161"
   config:
     server.labels:
       zone: bj
       dc: bjb
       rack: rack1
       host: host2
 - host: *********
   ssh_port: 22
   port: 20160
   deploy_dir: "/tidb-deploy/tikv-20160"
   data_dir: "/tidb-data/tikv-20160"
   config:
     server.labels:
       zone: sh
       dc: sha
       rack: rack1
       host: host1
     readpool.storage.use-unified-pool: true
     readpool.storage.low-concurrency: 10
     raftstore.raft-min-election-timeout-ticks: 50
     raftstore.raft-max-election-timeout-ticks: 60
monitoring_servers:
 - host: *********
grafana_servers:
 - host: *********
