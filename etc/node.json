{"__inputs": [{"name": "DS_TEST-CLUSTER", "label": "test-cluster", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}], "__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "4.1.2"}, {"type": "panel", "id": "graph", "name": "Graph", "version": ""}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}, {"type": "panel", "id": "singlestat", "name": "Singlestat", "version": ""}], "annotations": {"list": [{"datasource": "${DS_TEST-CLUSTER}", "enable": true, "expr": "ALERTS{instance=\"$host\", alertstate=\"firing\"}", "iconColor": "rgb(252, 5, 0)", "name": "<PERSON><PERSON>", "tagKeys": "severity", "textFormat": "{{ instance }} : {{alertstate}}", "titleFormat": "{{ alertname }}"}, {"datasource": "${DS_TEST-CLUSTER}", "enable": true, "expr": "ALERTS{instance=\"$host\",alertstate=\"pending\"}", "iconColor": "rgb(228, 242, 9)", "name": "Warning", "tagKeys": "severity", "textFormat": "{{ instance }} : {{ alertstate }}", "titleFormat": "{{ alertname }}"}]}, "description": "Prometheus for system metrics. \r\nLoad, CPU, RAM, network, process ... ", "editable": true, "gnetId": 159, "graphTooltip": 1, "hideControls": false, "id": null, "links": [{"asDropdown": false, "icon": "external link", "tags": [], "type": "dashboards"}], "refresh": "30s", "rows": [{"collapse": false, "height": "250px", "panels": [{"cacheTimeout": null, "colorBackground": false, "colorValue": true, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "format": "s", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "height": "50px", "id": 19, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "s", "postfixFontSize": "80%", "prefix": "", "prefixFontSize": "80%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "span": 3, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "targets": [{"calculatedInterval": "10m", "datasourceErrors": {}, "errors": {}, "expr": "node_time{instance=\"$host\"} - node_boot_time{instance=\"$host\"}", "interval": "5m", "intervalFactor": 1, "legendFormat": "", "metric": "", "prometheusLink": "/api/datasources/proxy/1/graph#%5B%7B%22expr%22%3A%22node_time%7Binstance%3D%5C%22%24host%5C%22%7D%20-%20node_boot_time%7Binstance%3D%5C%22%24host%5C%22%7D%22%2C%22range_input%22%3A%2243200s%22%2C%22end_input%22%3A%222015-9-18%2013%3A25%22%2C%22step_input%22%3A%22%22%2C%22tab%22%3A0%7D%5D", "refId": "A", "step": 300}], "thresholds": "300,3600", "title": "System Uptime", "transparent": false, "type": "singlestat", "valueFontSize": "80%", "valueMaps": [], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "datasource": "${DS_TEST-CLUSTER}", "editable": true, "error": false, "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "height": "55px", "id": 25, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "80%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "span": 3, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "targets": [{"expr": "count(node_cpu{mode=\"user\", instance=\"$host\"})", "interval": "5m", "intervalFactor": 1, "refId": "A", "step": 300}], "thresholds": "", "title": "Virtual CPUs", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "datasource": "${DS_TEST-CLUSTER}", "decimals": 2, "editable": true, "error": false, "format": "bytes", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "height": "55px", "id": 26, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "80%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "span": 3, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "targets": [{"expr": "node_memory_MemAvailable{instance=\"$host\"}", "interval": "", "intervalFactor": 1, "legendFormat": "", "metric": "node_memory_MemAvailable", "refId": "A", "step": 30}], "thresholds": "", "title": "RAM available", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": true, "colors": ["rgba(50, 172, 45, 0.97)", "rgba(237, 129, 40, 0.89)", "rgba(245, 54, 54, 0.9)"], "datasource": "${DS_TEST-CLUSTER}", "decimals": 0, "editable": true, "error": false, "format": "percent", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "height": "50px", "id": 9, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "80%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "span": 3, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": true, "lineColor": "rgb(31, 120, 193)", "show": true}, "targets": [{"calculatedInterval": "10m", "datasourceErrors": {}, "errors": {}, "expr": "(node_memory_MemAvailable{instance=\"$host\"} or (node_memory_MemFree{instance=\"$host\"} + node_memory_Buffers{instance=\"$host\"} + node_memory_Cached{instance=\"$host\"})) / node_memory_MemTotal{instance=\"$host\"} * 100", "interval": "5m", "intervalFactor": 1, "legendFormat": "", "metric": "node_mem", "prometheusLink": "/api/datasources/proxy/1/graph#%5B%7B%22expr%22%3A%22(node_memory_MemFree%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Buffers%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Cached%7Binstance%3D%5C%22%24host%5C%22%7D)%20%2F%20node_memory_MemTotal%7Binstance%3D%5C%22%24host%5C%22%7D%20*%20100%22%2C%22range_input%22%3A%2243201s%22%2C%22end_input%22%3A%222015-9-15%2013%3A54%22%2C%22step_input%22%3A%22%22%2C%22tab%22%3A0%7D%5D", "refId": "A", "step": 300}], "thresholds": "90,95", "title": "Memory Available", "transparent": false, "type": "singlestat", "valueFontSize": "80%", "valueMaps": [], "valueName": "current"}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 2, "editable": true, "error": false, "fill": 6, "grid": {}, "height": "260px", "id": 2, "instanceColors": {}, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 12, "stack": true, "steppedLine": false, "targets": [{"calculatedInterval": "2s", "datasourceErrors": {}, "errors": {}, "expr": "sum(rate(node_cpu{instance=\"$host\"}[$interval])) by (mode) * 100 / count_scalar(node_cpu{mode=\"user\", instance=\"$host\"}) or sum(irate(node_cpu{instance=\"$host\"}[5m])) by (mode) * 100 / count_scalar(node_cpu{mode=\"user\", instance=\"$host\"})", "intervalFactor": 1, "legendFormat": "{{ mode }}", "metric": "", "prometheusLink": "/api/datasources/proxy/1/graph#%5B%7B%22expr%22%3A%22sum(rate(node_cpu%7Binstance%3D%5C%22%24host%5C%22%7D%5B%24interval%5D))%20by%20(mode)%20*%20100%22%2C%22range_input%22%3A%223600s%22%2C%22end_input%22%3A%222015-10-22%2015%3A27%22%2C%22step_input%22%3A%22%22%2C%22stacked%22%3Atrue%2C%22tab%22%3A0%7D%5D", "refId": "A", "step": 1}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "CPU Usage", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percent", "label": "", "logBase": 1, "max": 100, "min": 0, "show": true}, {"format": "short", "logBase": 1, "max": null, "min": 0, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 2, "editable": true, "error": false, "fill": 2, "grid": {}, "id": 18, "instanceColors": {}, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": true, "show": true, "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"color": "#E24D42", "instance": "Load 1m"}, {"color": "#E0752D", "instance": "Load 5m"}, {"color": "#E5AC0E", "instance": "Load 15m"}], "span": 12, "stack": false, "steppedLine": false, "targets": [{"calculatedInterval": "10s", "datasourceErrors": {}, "errors": {}, "expr": "node_load1{instance=\"$host\"}", "intervalFactor": 1, "legendFormat": "Load 1m", "metric": "", "prometheusLink": "/api/datasources/proxy/1/graph#%5B%7B%22expr%22%3A%22node_load1%7Binstance%3D%5C%22%24host%5C%22%7D%22%2C%22range_input%22%3A%223601s%22%2C%22end_input%22%3A%222015-10-22%2015%3A27%22%2C%22step_input%22%3A%22%22%2C%22stacked%22%3Afalse%2C%22tab%22%3A0%7D%5D", "refId": "A", "step": 1, "target": ""}, {"calculatedInterval": "10s", "datasourceErrors": {}, "errors": {}, "expr": "node_load5{instance=\"$host\"}", "intervalFactor": 1, "legendFormat": "Load 5m", "metric": "", "prometheusLink": "/api/datasources/proxy/1/graph#%5B%7B%22expr%22%3A%22node_load5%7Binstance%3D%5C%22%24host%5C%22%7D%22%2C%22range_input%22%3A%223600s%22%2C%22end_input%22%3A%222015-10-22%2015%3A27%22%2C%22step_input%22%3A%22%22%2C%22stacked%22%3Afalse%2C%22tab%22%3A0%7D%5D", "refId": "B", "step": 1, "target": ""}, {"calculatedInterval": "10s", "datasourceErrors": {}, "errors": {}, "expr": "node_load15{instance=\"$host\"}", "intervalFactor": 1, "legendFormat": "Load 15m", "metric": "", "prometheusLink": "/api/datasources/proxy/1/graph#%5B%7B%22expr%22%3A%22node_load15%7Binstance%3D%5C%22%24host%5C%22%7D%22%2C%22range_input%22%3A%223600s%22%2C%22end_input%22%3A%222015-10-22%2015%3A27%22%2C%22step_input%22%3A%22%22%2C%22stacked%22%3Afalse%2C%22tab%22%3A0%7D%5D", "refId": "C", "step": 1, "target": ""}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Load Average", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "transparent": false, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": "", "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "none", "logBase": 1, "max": null, "min": 0, "show": true}]}], "repeat": null, "repeatIteration": null, "repeatRowId": null, "showTitle": false, "title": "System Stats", "titleSize": "h6"}, {"collapse": false, "height": "300px", "panels": [{"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 2, "editable": true, "error": false, "fill": 6, "grid": {}, "height": "", "id": 6, "instanceColors": {}, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"color": "#0A437C", "instance": "Used"}, {"color": "#5195CE", "instance": "Available"}, {"color": "#052B51", "instance": "Total", "legend": false, "stack": false}], "span": 6, "stack": true, "steppedLine": false, "targets": [{"calculatedInterval": "2s", "datasourceErrors": {}, "errors": {}, "expr": "node_memory_MemTotal{instance=\"$host\"}", "intervalFactor": 1, "legendFormat": "Total", "metric": "", "prometheusLink": "/api/datasources/proxy/1/graph#%5B%7B%22expr%22%3A%22node_memory_MemFree%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Buffers%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Cached%7Binstance%3D%5C%22%24host%5C%22%7D%22%2C%22range_input%22%3A%22900s%22%2C%22end_input%22%3A%222015-10-22%2015%3A25%22%2C%22step_input%22%3A%22%22%2C%22stacked%22%3Atrue%2C%22tab%22%3A0%7D%5D", "refId": "C", "step": 2, "target": ""}, {"calculatedInterval": "2s", "datasourceErrors": {}, "errors": {}, "expr": "node_memory_MemTotal{instance=\"$host\"} - (node_memory_MemAvailable{instance=\"$host\"} or (node_memory_MemFree{instance=\"$host\"} + node_memory_Buffers{instance=\"$host\"} + node_memory_Cached{instance=\"$host\"}))", "intervalFactor": 1, "legendFormat": "Used", "metric": "", "prometheusLink": "/api/datasources/proxy/1/graph#%5B%7B%22expr%22%3A%22node_memory_MemTotal%7Binstance%3D%5C%22%24host%5C%22%7D%20-%20(node_memory_MemFree%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Buffers%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Cached%7Binstance%3D%5C%22%24host%5C%22%7D)%22%2C%22range_input%22%3A%22900s%22%2C%22end_input%22%3A%222015-10-22%2015%3A25%22%2C%22step_input%22%3A%22%22%2C%22stacked%22%3Atrue%2C%22tab%22%3A0%7D%5D", "refId": "A", "step": 2, "target": ""}, {"calculatedInterval": "2s", "datasourceErrors": {}, "errors": {}, "expr": "node_memory_MemAvailable{instance=\"$host\"} or (node_memory_MemFree{instance=\"$host\"} + node_memory_Buffers{instance=\"$host\"} + node_memory_Cached{instance=\"$host\"})", "intervalFactor": 1, "legendFormat": "Available", "metric": "", "prometheusLink": "/api/datasources/proxy/1/graph#%5B%7B%22expr%22%3A%22node_memory_MemFree%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Buffers%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Cached%7Binstance%3D%5C%22%24host%5C%22%7D%22%2C%22range_input%22%3A%22900s%22%2C%22end_input%22%3A%222015-10-22%2015%3A25%22%2C%22step_input%22%3A%22%22%2C%22stacked%22%3Atrue%2C%22tab%22%3A0%7D%5D", "refId": "B", "step": 2, "target": ""}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Memory", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "transparent": false, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": "", "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "bytes", "logBase": 1, "max": null, "min": 0, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 2, "editable": true, "error": false, "fill": 6, "grid": {}, "height": "", "id": 29, "instanceColors": {}, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": true, "steppedLine": false, "targets": [{"calculatedInterval": "2s", "datasourceErrors": {}, "errors": {}, "expr": "node_memory_MemTotal{instance=\"$host\"} - (node_memory_MemFree{instance=\"$host\"} + node_memory_Buffers{instance=\"$host\"} + node_memory_Cached{instance=\"$host\"})", "intervalFactor": 1, "legendFormat": "Used", "metric": "", "prometheusLink": "/api/datasources/proxy/1/graph#%5B%7B%22expr%22%3A%22node_memory_MemTotal%7Binstance%3D%5C%22%24host%5C%22%7D%20-%20(node_memory_MemFree%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Buffers%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Cached%7Binstance%3D%5C%22%24host%5C%22%7D)%22%2C%22range_input%22%3A%22900s%22%2C%22end_input%22%3A%222015-10-22%2015%3A25%22%2C%22step_input%22%3A%22%22%2C%22stacked%22%3Atrue%2C%22tab%22%3A0%7D%5D", "refId": "A", "step": 2, "target": ""}, {"calculatedInterval": "2s", "datasourceErrors": {}, "errors": {}, "expr": "node_memory_MemFree{instance=\"$host\"}", "intervalFactor": 1, "legendFormat": "Free", "metric": "", "prometheusLink": "/api/datasources/proxy/1/graph#%5B%7B%22expr%22%3A%22node_memory_MemFree%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Buffers%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Cached%7Binstance%3D%5C%22%24host%5C%22%7D%22%2C%22range_input%22%3A%22900s%22%2C%22end_input%22%3A%222015-10-22%2015%3A25%22%2C%22step_input%22%3A%22%22%2C%22stacked%22%3Atrue%2C%22tab%22%3A0%7D%5D", "refId": "B", "step": 2, "target": ""}, {"calculatedInterval": "2s", "datasourceErrors": {}, "errors": {}, "expr": "node_memory_Buffers{instance=\"$host\"}", "intervalFactor": 1, "legendFormat": "Buffers", "metric": "", "prometheusLink": "/api/datasources/proxy/1/graph#%5B%7B%22expr%22%3A%22node_memory_MemFree%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Buffers%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Cached%7Binstance%3D%5C%22%24host%5C%22%7D%22%2C%22range_input%22%3A%22900s%22%2C%22end_input%22%3A%222015-10-22%2015%3A25%22%2C%22step_input%22%3A%22%22%2C%22stacked%22%3Atrue%2C%22tab%22%3A0%7D%5D", "refId": "D", "step": 2, "target": ""}, {"calculatedInterval": "2s", "datasourceErrors": {}, "errors": {}, "expr": "node_memory_Cached{instance=\"$host\"}", "intervalFactor": 1, "legendFormat": "<PERSON><PERSON><PERSON>", "metric": "", "prometheusLink": "/api/datasources/proxy/1/graph#%5B%7B%22expr%22%3A%22node_memory_MemFree%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Buffers%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Cached%7Binstance%3D%5C%22%24host%5C%22%7D%22%2C%22range_input%22%3A%22900s%22%2C%22end_input%22%3A%222015-10-22%2015%3A25%22%2C%22step_input%22%3A%22%22%2C%22stacked%22%3Atrue%2C%22tab%22%3A0%7D%5D", "refId": "E", "step": 2, "target": ""}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Memory Distribution", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "transparent": false, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": "", "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "bytes", "logBase": 1, "max": null, "min": 0, "show": true}]}, {"aliasColors": {}, "bars": true, "datasource": "${DS_TEST-CLUSTER}", "decimals": 2, "editable": true, "error": false, "fill": 2, "grid": {}, "id": 24, "instanceColors": {}, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": false, "show": true, "total": false, "values": true}, "lines": false, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"color": "#EF843C", "instance": "Forks"}], "span": 6, "stack": false, "steppedLine": false, "targets": [{"calculatedInterval": "2m", "datasourceErrors": {}, "errors": {}, "expr": "rate(node_forks{instance=\"$host\"}[$interval]) or irate(node_forks{instance=\"$host\"}[5m])", "intervalFactor": 1, "legendFormat": "Forks", "metric": "", "prometheusLink": "/api/datasources/proxy/1/graph#%5B%7B%22expr%22%3A%22node_procs_running%7Binstance%3D%5C%22%24host%5C%22%7D%22%2C%22range_input%22%3A%2243200s%22%2C%22end_input%22%3A%222015-9-18%2013%3A46%22%2C%22step_input%22%3A%22%22%2C%22stacked%22%3Atrue%2C%22tab%22%3A0%7D%5D", "refId": "A", "step": 2, "target": ""}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Forks", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "transparent": false, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": "", "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "none", "logBase": 1, "max": null, "min": 0, "show": true}]}, {"aliasColors": {}, "bars": true, "datasource": "${DS_TEST-CLUSTER}", "decimals": 2, "editable": true, "error": false, "fill": 2, "grid": {}, "id": 20, "instanceColors": {}, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": false, "show": true, "total": false, "values": true}, "lines": false, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"color": "#E24D42", "instance": "Processes blocked waiting for I/<PERSON> to complete"}, {"color": "#6ED0E0", "instance": "Processes in runnable state"}], "span": 6, "stack": true, "steppedLine": false, "targets": [{"calculatedInterval": "2m", "datasourceErrors": {}, "errors": {}, "expr": "node_procs_running{instance=\"$host\"}", "intervalFactor": 1, "legendFormat": "Processes in runnable state", "metric": "", "prometheusLink": "/api/datasources/proxy/1/graph#%5B%7B%22expr%22%3A%22node_procs_running%7Binstance%3D%5C%22%24host%5C%22%7D%22%2C%22range_input%22%3A%2243200s%22%2C%22end_input%22%3A%222015-9-18%2013%3A46%22%2C%22step_input%22%3A%22%22%2C%22stacked%22%3Atrue%2C%22tab%22%3A0%7D%5D", "refId": "A", "step": 2, "target": ""}, {"calculatedInterval": "2m", "datasourceErrors": {}, "errors": {}, "expr": "node_procs_blocked{instance=\"$host\"}", "intervalFactor": 1, "legendFormat": "Processes blocked waiting for I/<PERSON> to complete", "metric": "", "prometheusLink": "/api/datasources/proxy/1/graph#%5B%7B%22expr%22%3A%22node_procs_blocked%7Binstance%3D%5C%22%24host%5C%22%7D%22%2C%22range_input%22%3A%2243200s%22%2C%22end_input%22%3A%222015-9-18%2013%3A46%22%2C%22step_input%22%3A%22%22%2C%22stacked%22%3Atrue%2C%22tab%22%3A0%7D%5D", "refId": "B", "step": 2, "target": ""}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Processes", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "transparent": false, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": "", "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "none", "logBase": 1, "max": null, "min": 0, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 2, "editable": true, "error": false, "fill": 2, "grid": {}, "id": 27, "instanceColors": {}, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"calculatedInterval": "2m", "datasourceErrors": {}, "errors": {}, "expr": "rate(node_context_switches{instance=\"$host\"}[$interval]) or irate(node_context_switches{instance=\"$host\"}[5m])", "intervalFactor": 1, "legendFormat": "Context Switches", "metric": "", "prometheusLink": "/api/datasources/proxy/1/graph#%5B%7B%22expr%22%3A%22node_procs_running%7Binstance%3D%5C%22%24host%5C%22%7D%22%2C%22range_input%22%3A%2243200s%22%2C%22end_input%22%3A%222015-9-18%2013%3A46%22%2C%22step_input%22%3A%22%22%2C%22stacked%22%3Atrue%2C%22tab%22%3A0%7D%5D", "refId": "A", "step": 2, "target": ""}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Context Switches", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "transparent": false, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": "", "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "none", "logBase": 1, "max": null, "min": 0, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 2, "editable": true, "error": false, "fill": 2, "grid": {}, "id": 28, "instanceColors": {}, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"color": "#D683CE", "instance": "Interrupts"}], "span": 6, "stack": false, "steppedLine": false, "targets": [{"calculatedInterval": "2m", "datasourceErrors": {}, "errors": {}, "expr": "rate(node_intr{instance=\"$host\"}[$interval]) or irate(node_intr{instance=\"$host\"}[5m])", "intervalFactor": 1, "legendFormat": "Interrupts", "metric": "", "prometheusLink": "/api/datasources/proxy/1/graph#%5B%7B%22expr%22%3A%22node_procs_running%7Binstance%3D%5C%22%24host%5C%22%7D%22%2C%22range_input%22%3A%2243200s%22%2C%22end_input%22%3A%222015-9-18%2013%3A46%22%2C%22step_input%22%3A%22%22%2C%22stacked%22%3Atrue%2C%22tab%22%3A0%7D%5D", "refId": "A", "step": 2, "target": ""}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Interrupts", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "individual"}, "transparent": false, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": "", "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "none", "logBase": 1, "max": null, "min": 0, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 2, "editable": true, "error": false, "fill": 6, "grid": {}, "id": 21, "instanceColors": {}, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": true, "steppedLine": false, "targets": [{"calculatedInterval": "2s", "datasourceErrors": {}, "errors": {}, "expr": "rate(node_network_receive_bytes{instance=\"$host\", device!=\"lo\"}[$interval]) or irate(node_network_receive_bytes{instance=\"$host\", device!=\"lo\"}[5m])", "intervalFactor": 1, "legendFormat": "Inbound: {{ device }}", "metric": "", "prometheusLink": "/api/datasources/proxy/1/graph#%5B%7B%22expr%22%3A%22node_memory_MemFree%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Buffers%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Cached%7Binstance%3D%5C%22%24host%5C%22%7D%22%2C%22range_input%22%3A%22900s%22%2C%22end_input%22%3A%222015-10-22%2015%3A25%22%2C%22step_input%22%3A%22%22%2C%22stacked%22%3Atrue%2C%22tab%22%3A0%7D%5D", "refId": "B", "step": 2, "target": ""}, {"calculatedInterval": "2s", "datasourceErrors": {}, "errors": {}, "expr": "rate(node_network_transmit_bytes{instance=\"$host\", device!=\"lo\"}[$interval]) or irate(node_network_transmit_bytes{instance=\"$host\", device!=\"lo\"}[5m])", "intervalFactor": 1, "legendFormat": "Outbound: {{ device }}", "metric": "", "prometheusLink": "/api/datasources/proxy/1/graph#%5B%7B%22expr%22%3A%22node_memory_MemTotal%7Binstance%3D%5C%22%24host%5C%22%7D%20-%20(node_memory_MemFree%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Buffers%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Cached%7Binstance%3D%5C%22%24host%5C%22%7D)%22%2C%22range_input%22%3A%22900s%22%2C%22end_input%22%3A%222015-10-22%2015%3A25%22%2C%22step_input%22%3A%22%22%2C%22stacked%22%3Atrue%2C%22tab%22%3A0%7D%5D", "refId": "A", "step": 2, "target": ""}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Network Traffic", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "transparent": false, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "Bps", "label": "", "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "bytes", "logBase": 1, "max": null, "min": 0, "show": true}]}, {"aliasColors": {}, "bars": true, "datasource": "${DS_TEST-CLUSTER}", "decimals": 2, "editable": true, "error": false, "fill": 6, "grid": {}, "id": 22, "instanceColors": {}, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": false, "show": true, "sort": "min", "sortDesc": true, "total": false, "values": true}, "lines": false, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": true, "steppedLine": false, "targets": [{"calculatedInterval": "2s", "datasourceErrors": {}, "errors": {}, "expr": "sum(increase(node_network_receive_bytes{instance=\"$host\", device!=\"lo\"}[1h]))", "interval": "1h", "intervalFactor": 1, "legendFormat": "Received", "metric": "", "prometheusLink": "/api/datasources/proxy/1/graph#%5B%7B%22expr%22%3A%22node_memory_MemTotal%7Binstance%3D%5C%22%24host%5C%22%7D%20-%20(node_memory_MemFree%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Buffers%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Cached%7Binstance%3D%5C%22%24host%5C%22%7D)%22%2C%22range_input%22%3A%22900s%22%2C%22end_input%22%3A%222015-10-22%2015%3A25%22%2C%22step_input%22%3A%22%22%2C%22stacked%22%3Atrue%2C%22tab%22%3A0%7D%5D", "refId": "A", "step": 3600, "target": ""}, {"calculatedInterval": "2s", "datasourceErrors": {}, "errors": {}, "expr": "sum(increase(node_network_transmit_bytes{instance=\"$host\", device!=\"lo\"}[1h]))", "interval": "1h", "intervalFactor": 1, "legendFormat": "<PERSON><PERSON>", "metric": "", "prometheusLink": "/api/datasources/proxy/1/graph#%5B%7B%22expr%22%3A%22node_memory_MemTotal%7Binstance%3D%5C%22%24host%5C%22%7D%20-%20(node_memory_MemFree%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Buffers%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Cached%7Binstance%3D%5C%22%24host%5C%22%7D)%22%2C%22range_input%22%3A%22900s%22%2C%22end_input%22%3A%222015-10-22%2015%3A25%22%2C%22step_input%22%3A%22%22%2C%22stacked%22%3Atrue%2C%22tab%22%3A0%7D%5D", "refId": "B", "step": 3600, "target": ""}], "thresholds": [], "timeFrom": "24h", "timeShift": null, "title": "Network Utilization Hourly", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "transparent": false, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": "", "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "bytes", "logBase": 1, "max": null, "min": 0, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 2, "editable": true, "error": false, "fill": 6, "grid": {}, "id": 23, "instanceColors": {}, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"color": "#584477", "instance": "Used"}, {"color": "#AEA2E0", "instance": "Free"}], "span": 6, "stack": true, "steppedLine": false, "targets": [{"calculatedInterval": "2s", "datasourceErrors": {}, "errors": {}, "expr": "node_memory_SwapTotal{instance=\"$host\"} - node_memory_SwapFree{instance=\"$host\"}", "intervalFactor": 1, "legendFormat": "Used", "metric": "", "prometheusLink": "/api/datasources/proxy/1/graph#%5B%7B%22expr%22%3A%22node_memory_MemTotal%7Binstance%3D%5C%22%24host%5C%22%7D%20-%20(node_memory_MemFree%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Buffers%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Cached%7Binstance%3D%5C%22%24host%5C%22%7D)%22%2C%22range_input%22%3A%22900s%22%2C%22end_input%22%3A%222015-10-22%2015%3A25%22%2C%22step_input%22%3A%22%22%2C%22stacked%22%3Atrue%2C%22tab%22%3A0%7D%5D", "refId": "A", "step": 2, "target": ""}, {"calculatedInterval": "2s", "datasourceErrors": {}, "errors": {}, "expr": "node_memory_SwapFree{instance=\"$host\"}", "intervalFactor": 1, "legendFormat": "Free", "metric": "", "prometheusLink": "/api/datasources/proxy/1/graph#%5B%7B%22expr%22%3A%22node_memory_MemFree%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Buffers%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Cached%7Binstance%3D%5C%22%24host%5C%22%7D%22%2C%22range_input%22%3A%22900s%22%2C%22end_input%22%3A%222015-10-22%2015%3A25%22%2C%22step_input%22%3A%22%22%2C%22stacked%22%3Atrue%2C%22tab%22%3A0%7D%5D", "refId": "B", "step": 2, "target": ""}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "<PERSON><PERSON><PERSON>", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "transparent": false, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": "", "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "bytes", "logBase": 1, "max": null, "min": 0, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 2, "editable": true, "error": false, "fill": 2, "grid": {}, "id": 30, "instanceColors": {}, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"calculatedInterval": "2s", "datasourceErrors": {}, "errors": {}, "expr": "rate(node_vmstat_pswpin{instance=\"$host\"}[$interval]) * 4096 or irate(node_vmstat_pswpin{instance=\"$host\"}[5m]) * 4096", "intervalFactor": 1, "legendFormat": "Swap In", "metric": "", "prometheusLink": "/api/datasources/proxy/1/graph#%5B%7B%22expr%22%3A%22node_memory_MemTotal%7Binstance%3D%5C%22%24host%5C%22%7D%20-%20(node_memory_MemFree%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Buffers%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Cached%7Binstance%3D%5C%22%24host%5C%22%7D)%22%2C%22range_input%22%3A%22900s%22%2C%22end_input%22%3A%222015-10-22%2015%3A25%22%2C%22step_input%22%3A%22%22%2C%22stacked%22%3Atrue%2C%22tab%22%3A0%7D%5D", "refId": "A", "step": 2, "target": ""}, {"calculatedInterval": "2s", "datasourceErrors": {}, "errors": {}, "expr": "rate(node_vmstat_pswpout{instance=\"$host\"}[$interval]) * 4096 or irate(node_vmstat_pswpout{instance=\"$host\"}[5m]) * 4096", "intervalFactor": 1, "legendFormat": "Swap Out", "metric": "", "prometheusLink": "/api/datasources/proxy/1/graph#%5B%7B%22expr%22%3A%22node_memory_MemFree%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Buffers%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Cached%7Binstance%3D%5C%22%24host%5C%22%7D%22%2C%22range_input%22%3A%22900s%22%2C%22end_input%22%3A%222015-10-22%2015%3A25%22%2C%22step_input%22%3A%22%22%2C%22stacked%22%3Atrue%2C%22tab%22%3A0%7D%5D", "refId": "B", "step": 2, "target": ""}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Swap Activity", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "transparent": false, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "Bps", "label": "", "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "bytes", "logBase": 1, "max": null, "min": 0, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "description": "Number of TCP sockets in state inuse.", "fill": 1, "id": 32, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "node_sockstat_TCP_inuse{instance=\"$host\"}", "intervalFactor": 1, "legendFormat": "TCP In Use", "metric": "", "refId": "A", "step": 2}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "TCP In Use", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}], "repeat": null, "repeatIteration": null, "repeatRowId": null, "showTitle": false, "title": "New row", "titleSize": "h6"}, {"collapse": false, "height": "300", "panels": [{"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 2, "editable": true, "error": false, "fill": 2, "grid": {}, "id": 31, "instanceColors": {}, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": false, "max": true, "min": true, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 12, "stack": false, "steppedLine": false, "targets": [{"calculatedInterval": "2s", "datasourceErrors": {}, "errors": {}, "expr": "rate(node_vmstat_pgpgin{instance=\"$host\"}[$interval]) * 1024 or irate(node_vmstat_pgpgin{instance=\"$host\"}[5m]) * 1024", "intervalFactor": 1, "legendFormat": "Read", "metric": "", "prometheusLink": "/api/datasources/proxy/1/graph#%5B%7B%22expr%22%3A%22node_memory_MemTotal%7Binstance%3D%5C%22%24host%5C%22%7D%20-%20(node_memory_MemFree%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Buffers%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Cached%7Binstance%3D%5C%22%24host%5C%22%7D)%22%2C%22range_input%22%3A%22900s%22%2C%22end_input%22%3A%222015-10-22%2015%3A25%22%2C%22step_input%22%3A%22%22%2C%22stacked%22%3Atrue%2C%22tab%22%3A0%7D%5D", "refId": "A", "step": 1, "target": ""}, {"calculatedInterval": "2s", "datasourceErrors": {}, "errors": {}, "expr": "rate(node_vmstat_pgpgout{instance=\"$host\"}[$interval]) * 1024 or irate(node_vmstat_pgpgout{instance=\"$host\"}[5m]) * 1024", "intervalFactor": 1, "legendFormat": "Write", "metric": "", "prometheusLink": "/api/datasources/proxy/1/graph#%5B%7B%22expr%22%3A%22node_memory_MemFree%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Buffers%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Cached%7Binstance%3D%5C%22%24host%5C%22%7D%22%2C%22range_input%22%3A%22900s%22%2C%22end_input%22%3A%222015-10-22%2015%3A25%22%2C%22step_input%22%3A%22%22%2C%22stacked%22%3Atrue%2C%22tab%22%3A0%7D%5D", "refId": "B", "step": 1, "target": ""}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "I/O Throughput", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "transparent": false, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "Bps", "label": "", "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "bytes", "logBase": 1, "max": null, "min": 0, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 2, "editable": true, "error": false, "fill": 2, "grid": {}, "id": 35, "instanceColors": {}, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 200, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"calculatedInterval": "2s", "datasourceErrors": {}, "errors": {}, "expr": "rate(node_disk_io_time_ms{instance=\"$host\"}[1m]) / 1000", "intervalFactor": 1, "legendFormat": "{{ device }}", "metric": "node_disk_io_time_ms", "prometheusLink": "/api/datasources/proxy/1/graph#%5B%7B%22expr%22%3A%22node_memory_MemTotal%7Binstance%3D%5C%22%24host%5C%22%7D%20-%20(node_memory_MemFree%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Buffers%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Cached%7Binstance%3D%5C%22%24host%5C%22%7D)%22%2C%22range_input%22%3A%22900s%22%2C%22end_input%22%3A%222015-10-22%2015%3A25%22%2C%22step_input%22%3A%22%22%2C%22stacked%22%3Atrue%2C%22tab%22%3A0%7D%5D", "refId": "A", "step": 2, "target": ""}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "<PERSON>/O <PERSON>", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "transparent": false, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": "", "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "bytes", "logBase": 1, "max": null, "min": 0, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 2, "editable": true, "error": false, "fill": 2, "grid": {}, "id": 36, "instanceColors": {}, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 200, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"calculatedInterval": "2s", "datasourceErrors": {}, "errors": {}, "expr": "rate(node_disk_io_now{instance=\"$host\"}[1m])", "intervalFactor": 1, "legendFormat": "{{ device }}", "metric": "node_disk_io_time_ms", "prometheusLink": "/api/datasources/proxy/1/graph#%5B%7B%22expr%22%3A%22node_memory_MemTotal%7Binstance%3D%5C%22%24host%5C%22%7D%20-%20(node_memory_MemFree%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Buffers%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Cached%7Binstance%3D%5C%22%24host%5C%22%7D)%22%2C%22range_input%22%3A%22900s%22%2C%22end_input%22%3A%222015-10-22%2015%3A25%22%2C%22step_input%22%3A%22%22%2C%22stacked%22%3Atrue%2C%22tab%22%3A0%7D%5D", "refId": "A", "step": 2, "target": ""}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "I/O in Progress", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "transparent": false, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": "", "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "bytes", "logBase": 1, "max": null, "min": 0, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 2, "editable": true, "error": false, "fill": 2, "grid": {}, "id": 37, "instanceColors": {}, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 200, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"calculatedInterval": "2s", "datasourceErrors": {}, "errors": {}, "expr": "rate(node_disk_read_time_ms{instance=\"$host\"}[1m]) / rate(node_disk_reads_completed{instance=\"$host\"}[1m])", "intervalFactor": 1, "legendFormat": "{{ device }}", "metric": "node_disk_io_time_ms", "prometheusLink": "/api/datasources/proxy/1/graph#%5B%7B%22expr%22%3A%22node_memory_MemTotal%7Binstance%3D%5C%22%24host%5C%22%7D%20-%20(node_memory_MemFree%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Buffers%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Cached%7Binstance%3D%5C%22%24host%5C%22%7D)%22%2C%22range_input%22%3A%22900s%22%2C%22end_input%22%3A%222015-10-22%2015%3A25%22%2C%22step_input%22%3A%22%22%2C%22stacked%22%3Atrue%2C%22tab%22%3A0%7D%5D", "refId": "A", "step": 2, "target": ""}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "I/O Average Read Time", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "transparent": false, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": "", "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "bytes", "logBase": 1, "max": null, "min": 0, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 2, "editable": true, "error": false, "fill": 2, "grid": {}, "id": 38, "instanceColors": {}, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 200, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"calculatedInterval": "2s", "datasourceErrors": {}, "errors": {}, "expr": "rate(node_disk_write_time_ms{instance=\"$host\"}[1m]) / rate(node_disk_writes_completed{instance=\"$host\"}[1m])", "intervalFactor": 1, "legendFormat": "{{ device }}", "metric": "node_disk_io_time_ms", "prometheusLink": "/api/datasources/proxy/1/graph#%5B%7B%22expr%22%3A%22node_memory_MemTotal%7Binstance%3D%5C%22%24host%5C%22%7D%20-%20(node_memory_MemFree%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Buffers%7Binstance%3D%5C%22%24host%5C%22%7D%20%2B%20node_memory_Cached%7Binstance%3D%5C%22%24host%5C%22%7D)%22%2C%22range_input%22%3A%22900s%22%2C%22end_input%22%3A%222015-10-22%2015%3A25%22%2C%22step_input%22%3A%22%22%2C%22stacked%22%3Atrue%2C%22tab%22%3A0%7D%5D", "refId": "A", "step": 2, "target": ""}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "I/O Average Write Time", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "transparent": false, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": "", "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "bytes", "logBase": 1, "max": null, "min": 0, "show": true}]}], "repeat": null, "repeatIteration": null, "repeatRowId": null, "showTitle": true, "title": "I/O", "titleSize": "h6"}, {"collapse": false, "height": 250, "panels": [{"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "fill": 1, "id": 33, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "node_filefd_allocated{instance=\"$host\"}", "intervalFactor": 2, "legendFormat": "Allocated File Descriptor", "metric": "node_filefd_allocated", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Allocated File Descriptor", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "fill": 1, "id": 34, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "node_filefd_maximum{instance=\"$host\"}", "intervalFactor": 2, "legendFormat": "Maximum File Descriptor", "metric": "node_filefd_maximum", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Maximum File Descriptor", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}], "repeat": null, "repeatIteration": null, "repeatRowId": null, "showTitle": false, "title": "Dashboard Row", "titleSize": "h6"}], "schemaVersion": 14, "style": "dark", "tags": [], "templating": {"list": [{"allFormat": "glob", "auto": true, "auto_count": 200, "auto_min": "1s", "current": {"text": "5s", "value": "5s"}, "datasource": "test-cluster", "hide": 0, "includeAll": false, "label": "Interval", "multi": false, "multiFormat": "glob", "name": "interval", "options": [{"selected": false, "text": "auto", "value": "$__auto_interval"}, {"selected": false, "text": "1s", "value": "1s"}, {"selected": true, "text": "5s", "value": "5s"}, {"selected": false, "text": "1m", "value": "1m"}, {"selected": false, "text": "5m", "value": "5m"}, {"selected": false, "text": "1h", "value": "1h"}, {"selected": false, "text": "6h", "value": "6h"}, {"selected": false, "text": "1d", "value": "1d"}], "query": "1s,5s,1m,5m,1h,6h,1d", "refresh": 2, "type": "interval"}, {"allFormat": "glob", "allValue": null, "current": {}, "datasource": "${DS_TEST-CLUSTER}", "hide": 0, "includeAll": false, "label": "Host", "multi": false, "multiFormat": "regex values", "name": "host", "options": [], "query": "label_values(node_boot_time,instance)", "refresh": 1, "refresh_on_load": false, "regex": "", "sort": 3, "tagValuesQuery": "instance", "tags": [], "tagsQuery": "up", "type": "query", "useTags": false}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"collapse": false, "enable": true, "notice": false, "now": true, "refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "status": "Stable", "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"], "type": "timepicker"}, "timezone": "browser", "title": "TiDB Cluster - node", "version": 0}