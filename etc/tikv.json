{"__inputs": [{"name": "DS_TEST-CLUSTER", "label": "test-cluster", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}], "__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "4.1.2"}, {"type": "panel", "id": "graph", "name": "Graph", "version": ""}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}, {"type": "panel", "id": "singlestat", "name": "Singlestat", "version": ""}], "annotations": {"list": []}, "editable": true, "gnetId": null, "graphTooltip": 0, "hideControls": false, "id": null, "links": [{"icon": "external link", "tags": [], "type": "dashboards"}], "refresh": "1m", "rows": [{"collapse": false, "height": "300", "panels": [{"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 0, "grid": {}, "id": 34, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "total", "lines": false}], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(tikv_pd_heartbeat_tick_total{type=\"leader\"}) by (job)", "intervalFactor": 2, "legendFormat": "{{job}}", "metric": "tikv_pd_heartbeat_tick_total", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "leader", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 0, "grid": {}, "id": 37, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(tikv_pd_heartbeat_tick_total{type=\"region\"}) by (job)", "intervalFactor": 2, "legendFormat": "{{job}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "region", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": "", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 3, "grid": {}, "id": 33, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideZero": false, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(tikv_engine_size_bytes) by (type)", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "cf size", "tooltip": {"msResolution": false, "shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "decbytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 5, "grid": {}, "id": 56, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 0, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(tikv_engine_size_bytes) by (job)", "intervalFactor": 2, "legendFormat": "{{job}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "store size", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "decbytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"alert": {"conditions": [{"evaluator": {"params": [0], "type": "gt"}, "operator": {"type": "and"}, "query": {"datasourceId": 1, "model": {"expr": "sum(rate(tikv_channel_full_total[1m])) by (job, type)", "intervalFactor": 2, "legendFormat": "{{job}} - {{type}}", "metric": "", "refId": "A", "step": 10}, "params": ["A", "1m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "alerting", "frequency": "60s", "handler": 1, "message": "TiKV channel full", "name": "TiKV channel full alert", "noDataState": "no_data", "notifications": []}, "aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 3, "grid": {}, "id": 22, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_channel_full_total[1m])) by (job, type)", "intervalFactor": 2, "legendFormat": "{{job}} - {{type}}", "metric": "", "refId": "A", "step": 10}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0}], "timeFrom": null, "timeShift": null, "title": "channel full", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 0, "grid": {}, "id": 18, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_server_report_failure_msg_total[1m])) by (type,instance,job,store_id)", "intervalFactor": 2, "legendFormat": "{{job}} - {{type}} - to - {{store_id}}", "metric": "tikv_server_raft_store_msg_total", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "server report failures", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 57, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_region_written_keys_sum[1m])) by (job) / sum(rate(tikv_region_written_keys_count[1m])) by (job)", "intervalFactor": 2, "legendFormat": "{{job}}", "metric": "tikv_region_written_keys_bucket", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "region average written keys", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 58, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_region_written_bytes_sum[1m])) by (job) / sum(rate(tikv_region_written_bytes_count[1m])) by (job)", "intervalFactor": 2, "legendFormat": "{{job}}", "metric": "tikv_regi", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "region average written bytes", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 0, "grid": {}, "id": 75, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_region_written_keys_count[1m])) by (job)", "intervalFactor": 2, "legendFormat": "{{job}}", "metric": "tikv_region_written_keys_bucket", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "active written leaders", "tooltip": {"msResolution": false, "shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 0, "grid": {}, "id": 1481, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(1.0, sum(rate(tikv_raftstore_region_size_bucket[1m])) by (le))", "intervalFactor": 2, "legendFormat": "max", "metric": "", "refId": "A", "step": 10}, {"expr": "histogram_quantile(0.99, sum(rate(tikv_raftstore_region_size_bucket[1m])) by (le))", "intervalFactor": 2, "legendFormat": "99%", "metric": "", "refId": "B", "step": 10}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_raftstore_region_size_bucket[1m])) by (le))", "intervalFactor": 2, "legendFormat": "95%", "metric": "", "refId": "C", "step": 10}, {"expr": "sum(rate(tikv_raftstore_region_size_sum[1m])) / sum(rate(tikv_raftstore_region_size_count[1m])) ", "intervalFactor": 2, "legendFormat": "avg", "metric": "", "refId": "D", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "approximate region size", "tooltip": {"msResolution": false, "shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}], "repeat": null, "repeatIteration": null, "repeatRowId": null, "showTitle": true, "title": "Server", "titleSize": "h6"}, {"collapse": true, "height": "300", "panels": [{"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 0, "grid": {}, "id": 1164, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_raftstore_raft_process_duration_secs_bucket{type='tick'}[1m])) by (le))", "intervalFactor": 2, "legendFormat": "99%", "refId": "A", "step": 4}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_raftstore_raft_process_duration_secs_bucket{type='tick'}[1m])) by (le))", "intervalFactor": 2, "legendFormat": "95%", "refId": "C", "step": 4}, {"expr": "sum(rate(tikv_raftstore_raft_process_duration_secs_sum{type='tick'}[1m])) / sum(rate(tikv_raftstore_raft_process_duration_secs_count{type='tick'}[1m]))", "intervalFactor": 2, "legendFormat": "avg", "refId": "B", "step": 4}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 1}], "timeFrom": null, "timeShift": null, "title": "raft process tick duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 0, "grid": {}, "id": 1165, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(tikv_raftstore_raft_process_duration_secs_bucket{type='tick'}[1m])) by (le, job))", "intervalFactor": 2, "legendFormat": "{{job}}", "refId": "C", "step": 4}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 1}], "timeFrom": null, "timeShift": null, "title": "95% raft process tick duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"alert": {"conditions": [{"evaluator": {"params": [1], "type": "gt"}, "operator": {"type": "and"}, "query": {"datasourceId": 1, "model": {"expr": "histogram_quantile(0.99, sum(rate(tikv_raftstore_raft_process_duration_secs_bucket{type='ready'}[1m])) by (le))", "intervalFactor": 2, "legendFormat": "99%", "refId": "A", "step": 4}, "params": ["A", "1m", "now"]}, "reducer": {"params": [], "type": "max"}, "type": "query"}], "executionErrorState": "alerting", "frequency": "60s", "handler": 1, "message": "TiKV raft process ready duration 99th percentile is above 1s", "name": "TiKV raft process ready duration 99th percentile alert", "noDataState": "no_data", "notifications": []}, "aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 0, "grid": {}, "id": 12, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_raftstore_raft_process_duration_secs_bucket{type='ready'}[1m])) by (le))", "intervalFactor": 2, "legendFormat": "99%", "refId": "A", "step": 4}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_raftstore_raft_process_duration_secs_bucket{type='ready'}[1m])) by (le))", "intervalFactor": 2, "legendFormat": "95%", "refId": "C", "step": 4}, {"expr": "sum(rate(tikv_raftstore_raft_process_duration_secs_sum{type='ready'}[1m])) / sum(rate(tikv_raftstore_raft_process_duration_secs_count{type='ready'}[1m]))", "intervalFactor": 2, "legendFormat": "avg", "refId": "B", "step": 4}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 1}], "timeFrom": null, "timeShift": null, "title": "raft process ready duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 0, "grid": {}, "id": 118, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(tikv_raftstore_raft_process_duration_secs_bucket{type='ready'}[1m])) by (le, job))", "intervalFactor": 2, "legendFormat": "{{job}}", "refId": "C", "step": 4}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 1}], "timeFrom": null, "timeShift": null, "title": "95% raft process ready duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 5, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_raftstore_raft_ready_handled_total[1m])) by (type)", "intervalFactor": 2, "legendFormat": "{{type}}", "metric": "tikv_raftstore_raft_ready_handled_total", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "raft ready handled", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 1, "id": 108, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_raftstore_apply_proposal_bucket[1m])) by (le, job))", "intervalFactor": 2, "legendFormat": "{{job}}", "metric": "", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "raft proposals per ready", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 76, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_raftstore_proposal_total{type=~\"conf_change|transfer_leader\"}[1m])) by (type)", "intervalFactor": 2, "legendFormat": "{{type}}", "metric": "tikv_raftstore_proposal_total", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "raft admin proposals", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 7, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_raftstore_proposal_total{type=~\"local_read|normal|read_index\"}[1m])) by (type)", "intervalFactor": 2, "legendFormat": "{{type}}", "metric": "tikv_raftstore_proposal_total", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "raft read/write proposals", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 119, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_raftstore_proposal_total{type=~\"local_read|read_index\"}[1m])) by (job)", "intervalFactor": 2, "legendFormat": "{{job}}", "metric": "", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "raft read proposals per server", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 120, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_raftstore_proposal_total{type=\"normal\"}[1m])) by (job)", "intervalFactor": 2, "legendFormat": "{{job}}", "metric": "tikv_raftstore_proposal_total", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "raft write proposals per server", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 72, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.9999, sum(rate(tikv_raftstore_log_lag_bucket[1m])) by (le, job))", "intervalFactor": 2, "legendFormat": "{{job}}", "metric": "tikv_raftstore_log_lag_bucket", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "99.99% raft log lag", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 73, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.9999, sum(rate(tikv_raftstore_propose_log_size_bucket[1m])) by (le, job))", "intervalFactor": 2, "legendFormat": "{{job}}", "metric": "tikv_raftstore_propose_log_size_bucket", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "99.99% raft log size", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 77, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_raftstore_admin_cmd_total{status=\"success\", type!=\"compact\"}[1m]))  by (type)", "intervalFactor": 2, "legendFormat": "{{type}}", "metric": "tikv_raftstore_admin_cmd_total", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "raft admin commands", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 21, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_raftstore_admin_cmd_total{status=\"success\", type=\"compact\"}[1m]))  by (job)", "intervalFactor": 2, "legendFormat": "{{job}}", "metric": "tikv_raftstore_admin_cmd_total", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "raft compact commands", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 70, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_raftstore_check_split_total{type!=\"ignore\"}[1m])) by (type)", "intervalFactor": 2, "legendFormat": "{{type}}", "metric": "tikv_raftstore_check_split_total", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "check split", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 71, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.9999, sum(rate(tikv_raftstore_check_split_duration_seconds_bucket[1m])) by (le, job))", "intervalFactor": 2, "legendFormat": "{{job}}", "metric": "tikv_raftstore_check_split_duration_seconds_bucket", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "99.99% check split duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 11, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_raftstore_raft_sent_message_total[1m])) by (type)", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "raft sent messages", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 1, "id": 106, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_server_raft_message_recv_total[1m])) by (job)", "intervalFactor": 2, "legendFormat": "{{job}}", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "raft recv messages per server", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 25, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_raftstore_raft_sent_message_total{type=\"vote\"}[1m])) by (job)", "intervalFactor": 2, "legendFormat": "{{job}}", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "vote", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 1309, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_raftstore_raft_dropped_message_total[1m])) by (type)", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "raft dropped messages", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}], "repeat": null, "repeatIteration": null, "repeatRowId": null, "showTitle": true, "title": "Raft", "titleSize": "h6"}, {"collapse": true, "height": "250px", "panels": [{"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "editable": true, "error": false, "fill": 1, "grid": {}, "id": 31, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_raftstore_apply_log_duration_seconds_bucket[1m])) by (le))", "intervalFactor": 2, "legendFormat": " 99%", "metric": "", "refId": "A", "step": 4}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_raftstore_apply_log_duration_seconds_bucket[1m])) by (le))", "intervalFactor": 2, "legendFormat": "95%", "refId": "B", "step": 4}, {"expr": "sum(rate(tikv_raftstore_apply_log_duration_seconds_sum[1m])) / sum(rate(tikv_raftstore_apply_log_duration_seconds_count[1m])) ", "intervalFactor": 2, "legendFormat": "avg", "refId": "C", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "apply log duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "editable": true, "error": false, "fill": 1, "grid": {}, "id": 32, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(tikv_raftstore_apply_log_duration_seconds_bucket[1m])) by (le, job))", "intervalFactor": 2, "legendFormat": " {{job}}", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "95% apply log duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "editable": true, "error": false, "fill": 1, "grid": {}, "id": 39, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_raftstore_append_log_duration_seconds_bucket[1m])) by (le))", "intervalFactor": 2, "legendFormat": " 99%", "metric": "", "refId": "A", "step": 4}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_raftstore_append_log_duration_seconds_bucket[1m])) by (le))", "intervalFactor": 2, "legendFormat": "95%", "refId": "B", "step": 4}, {"expr": "sum(rate(tikv_raftstore_append_log_duration_seconds_sum[1m])) / sum(rate(tikv_raftstore_append_log_duration_seconds_count[1m]))", "intervalFactor": 2, "legendFormat": "avg", "refId": "C", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "append log duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "editable": true, "error": false, "fill": 1, "grid": {}, "id": 40, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(tikv_raftstore_append_log_duration_seconds_bucket[1m])) by (le, job))", "intervalFactor": 2, "legendFormat": "{{job}} ", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "95% append log duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "editable": true, "error": false, "fill": 1, "grid": {}, "id": 41, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_raftstore_request_wait_time_duration_secs_bucket[1m])) by (le))", "intervalFactor": 2, "legendFormat": "99%", "metric": "tikv_raftstore_request_wait_time_duration_secs_bucket", "refId": "A", "step": 4}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_raftstore_request_wait_time_duration_secs_bucket[1m])) by (le))", "intervalFactor": 2, "legendFormat": "95%", "refId": "B", "step": 4}, {"expr": "sum(rate(tikv_raftstore_request_wait_time_duration_secs_sum[1m])) / sum(rate(tikv_raftstore_request_wait_time_duration_secs_count[1m]))", "intervalFactor": 2, "legendFormat": "avg", "refId": "C", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "99% request wait duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "editable": true, "error": false, "fill": 1, "grid": {}, "id": 42, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(tikv_raftstore_request_wait_time_duration_secs_bucket[1m])) by (le, job))", "intervalFactor": 2, "legendFormat": "{{job}}", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "95% request wait duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}], "repeat": null, "repeatIteration": null, "repeatRowId": null, "showTitle": true, "title": "Raft Ready", "titleSize": "h6"}, {"collapse": true, "height": "300", "panels": [{"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 2, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_storage_command_total[1m])) by (type)", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "storage command total", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 10, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 8, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_storage_engine_async_request_total{status!~\"all|success\"}[1m])) by (status)", "intervalFactor": 2, "legendFormat": "{{status}}", "metric": "tikv_raftstore_raft_process_duration_secs_bucket", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "storage async request error", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 15, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_storage_engine_async_request_duration_seconds_bucket{type=\"snapshot\"}[1m])) by (le))", "intervalFactor": 2, "legendFormat": "99%", "refId": "A", "step": 4}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_storage_engine_async_request_duration_seconds_bucket{type=\"snapshot\"}[1m])) by (le))", "intervalFactor": 2, "legendFormat": "95%", "refId": "B", "step": 4}, {"expr": "sum(rate(tikv_storage_engine_async_request_duration_seconds_sum{type=\"snapshot\"}[1m])) / sum(rate(tikv_storage_engine_async_request_duration_seconds_count{type=\"snapshot\"}[1m]))", "intervalFactor": 2, "legendFormat": "avg", "refId": "C", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "storage async snapshot duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 109, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_storage_engine_async_request_duration_seconds_bucket{type=\"write\"}[1m])) by (le))", "intervalFactor": 2, "legendFormat": "99%", "refId": "A", "step": 4}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_storage_engine_async_request_duration_seconds_bucket{type=\"write\"}[1m])) by (le))", "intervalFactor": 2, "legendFormat": "95%", "refId": "B", "step": 4}, {"expr": "sum(rate(tikv_storage_engine_async_request_duration_seconds_sum{type=\"write\"}[1m])) / sum(rate(tikv_storage_engine_async_request_duration_seconds_count{type=\"write\"}[1m]))", "intervalFactor": 2, "legendFormat": "avg", "refId": "C", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "storage async write duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 1310, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "raft-95%", "yaxis": 2}], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_storage_batch_commands_total_bucket[30s])) by (le))", "intervalFactor": 2, "legendFormat": "99%", "refId": "A", "step": 4}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_storage_batch_commands_total_bucket[30s])) by (le))", "intervalFactor": 2, "legendFormat": "95%", "refId": "B", "step": 4}, {"expr": "sum(rate(tikv_storage_batch_commands_total_sum[30s])) / sum(rate(tikv_storage_batch_commands_total_count[30s]))", "intervalFactor": 2, "legendFormat": "avg", "refId": "C", "step": 4}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_raftstore_batch_snapshot_commands_total_bucket[30s])) by (le))", "intervalFactor": 2, "legendFormat": "raft-95%", "refId": "D", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "storage async batch snapshot", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": "Storage Batch Size", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": "Raftstore Batch Size", "logBase": 1, "max": null, "min": null, "show": true}]}], "repeat": null, "repeatIteration": null, "repeatRowId": null, "showTitle": true, "title": "Storage", "titleSize": "h6"}, {"collapse": true, "height": "300", "panels": [{"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 1, "height": "400", "id": 167, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "minSpan": 12, "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 12, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_scheduler_too_busy_total[1m])) by (stage)", "intervalFactor": 2, "legendFormat": "busy", "refId": "A", "step": 20}, {"expr": "sum(rate(tikv_scheduler_stage_total[1m])) by (stage)", "intervalFactor": 2, "legendFormat": "{{stage}}", "refId": "B", "step": 20}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "scheduler stage total", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "height": "", "id": 1, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "minSpan": 6, "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_scheduler_commands_pri_total[1m])) by (priority)", "intervalFactor": 2, "legendFormat": "{{priority}}", "metric": "", "refId": "A", "step": 40}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "scheduler priority commands", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "transparent": false, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "height": "", "id": 193, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "minSpan": 6, "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(tikv_scheduler_contex_total) by (job)", "intervalFactor": 2, "legendFormat": "{{job}}", "metric": "", "refId": "A", "step": 40}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "scheduler pending commands", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "transparent": false, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}], "repeat": null, "repeatIteration": null, "repeatRowId": null, "showTitle": true, "title": "Scheduler", "titleSize": "h6"}, {"collapse": true, "height": "300", "panels": [{"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 1, "height": "400", "id": 168, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "minSpan": 12, "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 12, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_scheduler_too_busy_total{type=\"$command\"}[1m]))", "intervalFactor": 2, "legendFormat": "busy", "refId": "A", "step": 4}, {"expr": "sum(rate(tikv_scheduler_stage_total{type=\"$command\"}[1m])) by (stage)", "intervalFactor": 2, "legendFormat": "{{stage}}", "refId": "B", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "scheduler stage total", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 3, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": false, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "minSpan": null, "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_scheduler_command_duration_seconds_bucket{type=\"$command\"}[1m])) by (le))", "intervalFactor": 2, "legendFormat": "99%", "metric": "", "refId": "A", "step": 10}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_scheduler_command_duration_seconds_bucket{type=\"$command\"}[1m])) by (le))", "intervalFactor": 2, "legendFormat": "95%", "metric": "", "refId": "B", "step": 10}, {"expr": "sum(rate(tikv_scheduler_command_duration_seconds_sum{type=\"$command\"}[1m])) / sum(rate(tikv_scheduler_command_duration_seconds_count{type=\"$command\"}[1m])) ", "intervalFactor": 2, "legendFormat": "avg", "metric": "", "refId": "C", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "scheduler command duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 194, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": false, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "minSpan": null, "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_scheduler_latch_wait_duration_seconds_bucket{type=\"$command\"}[1m])) by (le))", "intervalFactor": 2, "legendFormat": "99%", "metric": "", "refId": "A", "step": 10}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_scheduler_latch_wait_duration_seconds_bucket{type=\"$command\"}[1m])) by (le))", "intervalFactor": 2, "legendFormat": "95%", "metric": "", "refId": "B", "step": 10}, {"expr": "sum(rate(tikv_scheduler_latch_wait_duration_seconds_sum{type=\"$command\"}[1m])) / sum(rate(tikv_scheduler_latch_wait_duration_seconds_count{type=\"$command\"}[1m])) ", "intervalFactor": 2, "legendFormat": "avg", "metric": "", "refId": "C", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "scheduler latch wait duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 195, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": false, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "minSpan": null, "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_scheduler_kv_command_key_read_bucket{type=\"$command\"}[1m])) by (le))", "intervalFactor": 2, "legendFormat": "99%", "metric": "kv_command_key", "refId": "A", "step": 10}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_scheduler_kv_command_key_read_bucket{type=\"$command\"}[1m])) by (le))", "intervalFactor": 2, "legendFormat": "95%", "metric": "", "refId": "B", "step": 10}, {"expr": "sum(rate(tikv_scheduler_kv_command_key_read_sum{type=\"$command\"}[1m])) / sum(rate(tikv_scheduler_kv_command_key_read_count{type=\"$command\"}[1m])) ", "intervalFactor": 2, "legendFormat": "avg", "metric": "", "refId": "C", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "scheduler keys read", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 373, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": false, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "minSpan": null, "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_scheduler_kv_command_key_write_bucket{type=\"$command\"}[1m])) by (le))", "intervalFactor": 2, "legendFormat": "99%", "metric": "kv_command_key", "refId": "A", "step": 10}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_scheduler_kv_command_key_write_bucket{type=\"$command\"}[1m])) by (le))", "intervalFactor": 2, "legendFormat": "95%", "metric": "", "refId": "B", "step": 10}, {"expr": "sum(rate(tikv_scheduler_kv_command_key_write_sum{type=\"$command\"}[1m])) / sum(rate(tikv_scheduler_kv_command_key_write_count{type=\"$command\"}[1m])) ", "intervalFactor": 2, "legendFormat": "avg", "metric": "", "refId": "C", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "scheduler keys written", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 560, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": false, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "minSpan": null, "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_scheduler_kv_scan_details{req=\"$command\"}[1m])) by (tag)", "intervalFactor": 2, "legendFormat": "{{tag}}", "metric": "", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "scheduler scan details", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 675, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": false, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "minSpan": null, "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_scheduler_kv_scan_details{req=\"$command\", cf=\"lock\"}[1m])) by (tag)", "intervalFactor": 2, "legendFormat": "{{tag}}", "metric": "", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "scheduler scan details [lock]", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 829, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": false, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "minSpan": null, "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_scheduler_kv_scan_details{req=\"$command\", cf=\"write\"}[1m])) by (tag)", "intervalFactor": 2, "legendFormat": "{{tag}}", "metric": "", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "scheduler scan details [write]", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 830, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": false, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "minSpan": null, "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_scheduler_kv_scan_details{req=\"$command\", cf=\"default\"}[1m])) by (tag)", "intervalFactor": 2, "legendFormat": "{{tag}}", "metric": "", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "scheduler scan details [default]", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}], "repeat": "command", "repeatIteration": null, "repeatRowId": null, "showTitle": true, "title": "Scheduler - $command", "titleSize": "h6"}, {"collapse": true, "height": "250px", "panels": [{"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 5, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 16, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 4, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_coprocessor_request_duration_seconds_bucket{req=\"select\"}[1m])) by (le))", "intervalFactor": 2, "legendFormat": "99%", "refId": "A", "step": 4}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_coprocessor_request_duration_seconds_bucket{req=\"select\"}[1m])) by (le))", "intervalFactor": 2, "legendFormat": "95%", "refId": "B", "step": 4}, {"expr": " sum(rate(tikv_coprocessor_request_duration_seconds_sum{req=\"select\"}[1m])) /  sum(rate(tikv_coprocessor_request_duration_seconds_count{req=\"select\"}[1m]))", "intervalFactor": 2, "legendFormat": "avg", "refId": "C", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "coprocessor table request duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 5, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 13, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 4, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_coprocessor_request_duration_seconds_bucket{req=\"index\"}[1m])) by (le))", "intervalFactor": 2, "legendFormat": "99%", "metric": "tikv_coprocessor_request_duration_seconds_bucket", "refId": "A", "step": 4}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_coprocessor_request_duration_seconds_bucket{req=\"index\"}[1m])) by (le))", "intervalFactor": 2, "legendFormat": "95%", "refId": "B", "step": 4}, {"expr": "sum(rate(tikv_coprocessor_request_duration_seconds_sum{req=\"index\"}[1m])) / sum(rate(tikv_coprocessor_request_duration_seconds_count{req=\"index\"}[1m]))", "intervalFactor": 2, "legendFormat": "avg", "refId": "C", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "coprocessor index request duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 5, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 115, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 4, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(tikv_coprocessor_request_duration_seconds_bucket[1m])) by (le, job,req))", "intervalFactor": 2, "legendFormat": "{{job}}-{{req}}", "metric": "tikv_coprocessor_request_duration_seconds_bucket", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "95% coprocessor request duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 5, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 111, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 4, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_coprocessor_request_wait_seconds_bucket{req=\"select\"}[1m])) by (le))", "intervalFactor": 2, "legendFormat": "99%", "refId": "A", "step": 4}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_coprocessor_request_wait_seconds_bucket{req=\"select\"}[1m])) by (le))", "intervalFactor": 2, "legendFormat": "95%", "refId": "B", "step": 4}, {"expr": " sum(rate(tikv_coprocessor_request_wait_seconds_sum{req=\"select\"}[1m])) /  sum(rate(tikv_coprocessor_request_wait_seconds_count{req=\"select\"}[1m]))", "intervalFactor": 2, "legendFormat": "avg", "refId": "C", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "coprocessor table wait duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 5, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 112, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 4, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_coprocessor_request_wait_seconds_bucket{req=\"index\"}[1m])) by (le))", "intervalFactor": 2, "legendFormat": "99%", "refId": "A", "step": 4}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_coprocessor_request_wait_seconds_bucket{req=\"index\"}[1m])) by (le))", "intervalFactor": 2, "legendFormat": "95%", "refId": "B", "step": 4}, {"expr": " sum(rate(tikv_coprocessor_request_wait_seconds_sum{req=\"index\"}[1m])) /  sum(rate(tikv_coprocessor_request_wait_seconds_count{req=\"index\"}[1m]))", "intervalFactor": 2, "legendFormat": "avg", "refId": "C", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "coprocessor index wait duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 5, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 116, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 4, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(tikv_coprocessor_request_wait_seconds_bucket[1m])) by (le, job,req))", "intervalFactor": 2, "legendFormat": "{{job}}-{{req}}", "refId": "B", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "95% coprocessor wait duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 5, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 113, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 4, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_coprocessor_request_handle_seconds_bucket{req=\"select\"}[1m])) by (le))", "intervalFactor": 2, "legendFormat": "99%", "refId": "A", "step": 4}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_coprocessor_request_handle_seconds_bucket{req=\"select\"}[1m])) by (le))", "intervalFactor": 2, "legendFormat": "95%", "refId": "B", "step": 4}, {"expr": " sum(rate(tikv_coprocessor_request_handle_seconds_sum{req=\"select\"}[1m])) /  sum(rate(tikv_coprocessor_request_handle_seconds_count{req=\"select\"}[1m]))", "intervalFactor": 2, "legendFormat": "avg", "refId": "C", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "coprocessor table handle duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 5, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 114, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 4, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_coprocessor_request_handle_seconds_bucket{req=\"index\"}[1m])) by (le))", "intervalFactor": 2, "legendFormat": "99%", "refId": "A", "step": 4}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_coprocessor_request_handle_seconds_bucket{req=\"index\"}[1m])) by (le))", "intervalFactor": 2, "legendFormat": "95%", "refId": "B", "step": 4}, {"expr": " sum(rate(tikv_coprocessor_request_handle_seconds_sum{req=\"index\"}[1m])) /  sum(rate(tikv_coprocessor_request_handle_seconds_count{req=\"index\"}[1m]))", "intervalFactor": 2, "legendFormat": "avg", "refId": "C", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "coprocessor index handle duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 5, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 117, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 4, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(tikv_coprocessor_request_handle_seconds_bucket[1m])) by (le, job,req))", "intervalFactor": 2, "legendFormat": "{{job}}-{{req}}", "refId": "B", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "95% coprocessor handle duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 52, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_coprocessor_scan_keys_bucket[1m])) by (req)", "interval": "", "intervalFactor": 2, "legendFormat": "{{req}}", "metric": "tikv_coprocessor_scan_keys_bucket", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "coprocessor scan keys", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 551, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_coprocessor_executor_count[1m])) by (type)", "interval": "", "intervalFactor": 2, "legendFormat": "{{type}}", "metric": "", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "coprocessor executor count", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 2, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 74, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_coprocessor_request_error[1m])) by (reason)", "interval": "", "intervalFactor": 2, "legendFormat": "{{reason}}", "metric": "tikv_coprocessor_request_error", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "coprocessor request errors", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 2, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 550, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_coprocessor_pending_request[1m])) by (req, priority)", "interval": "", "intervalFactor": 2, "legendFormat": "{{ req }} - {{priority}}", "metric": "tikv_coprocessor_request_error", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "coprocessor pending requests", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 552, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_coprocessor_scan_details{req=\"select\"}[1m])) by (tag)", "interval": "", "intervalFactor": 2, "legendFormat": "{{tag}}", "metric": "scan_details", "refId": "B", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "coprocessor table scan details", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 122, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "repeat": null, "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_coprocessor_scan_details{cf=\"lock\", req=\"select\"}[1m])) by (tag)", "interval": "", "intervalFactor": 2, "legendFormat": "{{tag}}", "metric": "scan_details", "refId": "B", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "coprocessor table scan details [lock]", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 555, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_coprocessor_scan_details{cf=\"write\", req=\"select\"}[1m])) by (tag)", "interval": "", "intervalFactor": 2, "legendFormat": "{{tag}}", "metric": "scan_details", "refId": "B", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "coprocessor table scan details [write]", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 556, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_coprocessor_scan_details{cf=\"default\", req=\"select\"}[1m])) by (tag)", "interval": "", "intervalFactor": 2, "legendFormat": "{{tag}}", "metric": "scan_details", "refId": "B", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "coprocessor table scan details [default]", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 553, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_coprocessor_scan_details{req=\"index\"}[1m])) by (tag)", "interval": "", "intervalFactor": 2, "legendFormat": "{{tag}}", "metric": "scan_details", "refId": "B", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "coprocessor index scan details", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 554, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "repeat": "cf", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_coprocessor_scan_details{cf=\"lock\", req=\"index\"}[1m])) by (tag)", "interval": "", "intervalFactor": 2, "legendFormat": "{{tag}}", "metric": "scan_details", "refId": "B", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "coprocessor index scan details - [lock]", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 557, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_coprocessor_scan_details{cf=\"write\", req=\"index\"}[1m])) by (tag)", "interval": "", "intervalFactor": 2, "legendFormat": "{{tag}}", "metric": "scan_details", "refId": "B", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "coprocessor index scan details - [write]", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 558, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_coprocessor_scan_details{cf=\"default\", req=\"index\"}[1m])) by (tag)", "interval": "", "intervalFactor": 2, "legendFormat": "{{tag}}", "metric": "scan_details", "refId": "B", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "coprocessor index scan details - [default]", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}], "repeat": null, "repeatIteration": null, "repeatRowId": null, "showTitle": true, "title": "Coprocessor", "titleSize": "h6"}, {"collapse": true, "height": "250px", "panels": [{"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 26, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(1.0, sum(rate(tikv_storage_mvcc_versions_bucket[1m])) by (le))", "intervalFactor": 2, "legendFormat": " max", "metric": "", "refId": "A", "step": 4}, {"expr": "histogram_quantile(0.99, sum(rate(tikv_storage_mvcc_versions_bucket[1m])) by (le))", "intervalFactor": 2, "legendFormat": "99%", "metric": "", "refId": "B", "step": 4}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_storage_mvcc_versions_bucket[1m])) by (le))", "intervalFactor": 2, "legendFormat": " 95%", "metric": "", "refId": "C", "step": 4}, {"expr": "sum(rate(tikv_storage_mvcc_versions_sum[1m])) / sum(rate(tikv_storage_mvcc_versions_count[1m])) ", "intervalFactor": 2, "legendFormat": "avg", "metric": "", "refId": "D", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "MVCC Versions", "tooltip": {"msResolution": false, "shared": true, "sort": 2, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 559, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(1.0, sum(rate(tikv_storage_mvcc_gc_delete_versions_bucket[1m])) by (le))", "intervalFactor": 2, "legendFormat": " max", "metric": "", "refId": "A", "step": 4}, {"expr": "histogram_quantile(0.99, sum(rate(tikv_storage_mvcc_gc_delete_versions_bucket[1m])) by (le))", "intervalFactor": 2, "legendFormat": "99%", "metric": "", "refId": "B", "step": 4}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_storage_mvcc_gc_delete_versions_bucket[1m])) by (le))", "intervalFactor": 2, "legendFormat": " 95%", "metric": "", "refId": "C", "step": 4}, {"expr": "sum(rate(tikv_storage_mvcc_gc_delete_versions_sum[1m])) / sum(rate(tikv_storage_mvcc_gc_delete_versions_count[1m])) ", "intervalFactor": 2, "legendFormat": "avg", "metric": "", "refId": "D", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "MVCC Delete Versions", "tooltip": {"msResolution": false, "shared": true, "sort": 2, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 1, "id": 121, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_storage_command_total{type=\"gc\"}[1m]))", "intervalFactor": 2, "legendFormat": "total", "metric": "tikv_storage_command_total", "refId": "A", "step": 4}, {"expr": "sum(rate(tikv_storage_gc_skipped_counter[1m]))", "intervalFactor": 2, "legendFormat": "skipped", "metric": "tikv_storage_gc_skipped_counter", "refId": "B", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "GC Commands", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 2, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 966, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_tikvclient_gc_worker_actions_total[1m])) by (type)", "intervalFactor": 2, "legendFormat": "{{type}}", "metric": "", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "GC Worker Actions", "tooltip": {"msResolution": false, "shared": true, "sort": 2, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "datasource": "${DS_TEST-CLUSTER}", "decimals": 0, "editable": true, "error": false, "format": "s", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "id": 27, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "null", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "span": 3, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "targets": [{"expr": "max(tidb_tikvclient_gc_config{type=\"tikv_gc_life_time\"})", "interval": "", "intervalFactor": 2, "refId": "A", "step": 60}], "thresholds": "", "title": "GC LifeTime", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "datasource": "${DS_TEST-CLUSTER}", "decimals": 0, "editable": true, "error": false, "format": "s", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "id": 28, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "null", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "span": 3, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "targets": [{"expr": "max(tidb_tikvclient_gc_config{type=\"tikv_gc_run_interval\"})", "intervalFactor": 2, "refId": "A", "step": 60}], "thresholds": "", "title": "GC interval", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}], "repeat": null, "repeatIteration": null, "repeatRowId": null, "showTitle": true, "title": "GC", "titleSize": "h6"}, {"collapse": true, "height": "250px", "panels": [{"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "editable": true, "error": false, "fill": 1, "grid": {}, "id": 35, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 4, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(delta(tikv_raftstore_raft_sent_message_total{type=\"snapshot\"}[1m]))", "intervalFactor": 2, "legendFormat": " ", "refId": "A", "step": 60}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "rate snapshot message", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "opm", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "editable": true, "error": false, "fill": 1, "grid": {}, "id": 36, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 4, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_server_send_snapshot_duration_seconds_bucket[1m])) by (le))", "intervalFactor": 2, "legendFormat": "send", "refId": "A", "step": 60}, {"expr": "histogram_quantile(0.99, sum(rate(tikv_raftstore_snapshot_duration_seconds_bucket{type=\"apply\"}[1m])) by (le))", "intervalFactor": 2, "legendFormat": "apply", "refId": "B", "step": 60}, {"expr": "histogram_quantile(0.99, sum(rate(tikv_raftstore_snapshot_duration_seconds_bucket{type=\"generate\"}[1m])) by (le))", "intervalFactor": 2, "legendFormat": "generate", "refId": "C", "step": 60}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "99% handle snapshot duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "editable": true, "error": false, "fill": 1, "grid": {}, "id": 38, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 4, "stack": false, "steppedLine": true, "targets": [{"expr": "sum(tikv_raftstore_snapshot_traffic_total) by (type)", "intervalFactor": 2, "legendFormat": "{{type}}", "metric": "", "refId": "A", "step": 60}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "snapshot state count", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "editable": true, "error": false, "fill": 1, "grid": {}, "id": 44, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.9999, sum(rate(tikv_snapshot_size_bucket[1m])) by (le))", "intervalFactor": 2, "legendFormat": "size", "metric": "tikv_snapshot_size_bucket", "refId": "A", "step": 40}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "99.99% snapshot size", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "editable": true, "error": false, "fill": 1, "grid": {}, "id": 43, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.9999, sum(rate(tikv_snapshot_kv_count_bucket[1m])) by (le))", "intervalFactor": 2, "legendFormat": "count", "metric": "tikv_snapshot_kv_count_bucket", "refId": "A", "step": 40}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "99.99% snapshot kv count", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}], "repeat": null, "repeatIteration": null, "repeatRowId": null, "showTitle": true, "title": "Snapshot", "titleSize": "h6"}, {"collapse": true, "height": "300", "panels": [{"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 59, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": false, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 400, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_worker_handled_task_total[1m])) by (name)", "intervalFactor": 2, "legendFormat": "{{name}}", "metric": "tikv_pd_heartbeat_tick_total", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Worker Handled Tasks", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 1395, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": false, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 400, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_worker_pending_task_total[1m])) by (name)", "intervalFactor": 2, "legendFormat": "{{name}}", "metric": "tikv_pd_heartbeat_tick_total", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Worker Pending Tasks", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}], "repeat": null, "repeatIteration": null, "repeatRowId": null, "showTitle": true, "title": "Task", "titleSize": "h6"}, {"collapse": true, "height": 250, "panels": [{"alert": {"conditions": [{"evaluator": {"params": [0.8], "type": "gt"}, "operator": {"type": "and"}, "query": {"datasourceId": 1, "model": {"expr": "sum(rate(tikv_thread_cpu_seconds_total{name=~\"raftstore_.*\"}[1m])) by (job, name)", "intervalFactor": 2, "legendFormat": "{{job}}", "metric": "tikv_thread_cpu_seconds_total", "refId": "A", "step": 20}, "params": ["A", "1m", "now"]}, "reducer": {"params": [], "type": "max"}, "type": "query"}], "executionErrorState": "alerting", "frequency": "60s", "handler": 1, "message": "TiKV raftstore thread CPU usage is high", "name": "TiKV raft store CPU alert", "noDataState": "no_data", "notifications": []}, "aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 0, "grid": {}, "id": 61, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_thread_cpu_seconds_total{name=~\"raftstore_.*\"}[1m])) by (job, name)", "intervalFactor": 2, "legendFormat": "{{job}}", "metric": "tikv_thread_cpu_seconds_total", "refId": "A", "step": 4}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0.8}], "timeFrom": null, "timeShift": null, "title": "raft store CPU", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 79, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_thread_cpu_seconds_total{name=\"apply_worker\"}[1m])) by (job)", "intervalFactor": 2, "legendFormat": "{{job}}", "metric": "tikv_thread_cpu_seconds_total", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "async apply CPU", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "editable": true, "error": false, "fill": 0, "grid": {}, "id": 63, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_thread_cpu_seconds_total{name=~\"storage_schedul.*\"}[1m])) by (job)", "intervalFactor": 2, "legendFormat": "{{job}}", "metric": "tikv_thread_cpu_seconds_total", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "scheduler CPU", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 0, "grid": {}, "id": 64, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_thread_cpu_seconds_total{name=~\"sched_worker.*\"}[1m])) by (job)", "intervalFactor": 2, "legendFormat": "{{job}}", "metric": "tikv_thread_cpu_seconds_total", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "scheduler worker CPU", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 0, "grid": {}, "id": 78, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_thread_cpu_seconds_total{name=~\"endpoint.*\"}[1m])) by (job)", "interval": "", "intervalFactor": 2, "legendFormat": "{{job}}", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "coprocessor CPU", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 0, "grid": {}, "id": 67, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": false, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_thread_cpu_seconds_total{name=~\"snapshot_worker.*\"}[1m])) by (job)", "intervalFactor": 2, "legendFormat": "{{job}}", "metric": "tikv_thread_cpu_seconds_total", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "snapshot worker CPU", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 0, "grid": {}, "id": 68, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_thread_cpu_seconds_total{name=~\"split_check.*\"}[1m])) by (job)", "intervalFactor": 2, "legendFormat": "{{job}}", "metric": "tikv_thread_cpu_seconds_total", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "split check CPU", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "editable": true, "error": false, "fill": 0, "grid": {}, "id": 69, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "sort": null, "sortDesc": null, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "max(rate(tikv_thread_cpu_seconds_total{name=~\"rocksdb.*\"}[1m])) by (job)", "intervalFactor": 2, "legendFormat": "{{job}}", "metric": "tikv_thread_cpu_seconds_total", "refId": "A", "step": 4}], "thresholds": [{"colorMode": "warning", "fill": true, "line": true, "op": "gt", "value": 1}, {"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 4}], "timeFrom": null, "timeShift": null, "title": "rocksdb CPU", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 1, "id": 105, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_thread_cpu_seconds_total{name=~\"grpc.*\"}[1m])) by (job)", "intervalFactor": 2, "legendFormat": "{{job}}", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "grpc poll CPU", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}], "repeat": null, "repeatIteration": null, "repeatRowId": null, "showTitle": true, "title": "Thread CPU", "titleSize": "h6"}, {"collapse": true, "height": "300", "panels": [{"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 1, "id": 138, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_engine_memtable_efficiency{db=\"$db\", type=\"memtable_hit\"}[1m]))", "intervalFactor": 2, "legendFormat": "memtable", "metric": "", "refId": "B", "step": 10}, {"expr": "sum(rate(tikv_engine_cache_efficiency{db=\"$db\", type=~\"block_cache_data_hit|block_cache_filter_hit\"}[1m]))", "intervalFactor": 2, "legendFormat": "block_cache", "metric": "", "refId": "E", "step": 10}, {"expr": "sum(rate(tikv_engine_get_served{db=\"$db\", type=\"get_hit_l0\"}[1m]))", "intervalFactor": 2, "legendFormat": "l0", "refId": "A", "step": 10}, {"expr": "sum(rate(tikv_engine_get_served{db=\"$db\", type=\"get_hit_l1\"}[1m]))", "intervalFactor": 2, "legendFormat": "l1", "refId": "C", "step": 10}, {"expr": "sum(rate(tikv_engine_get_served{db=\"$db\", type=\"get_hit_l2_and_up\"}[1m]))", "intervalFactor": 2, "legendFormat": "l2_and_up", "refId": "F", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Get Operations", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 0, "id": 82, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "minSpan": null, "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(tikv_engine_get_micro_seconds{db=\"$db\",type=\"get_max\"})", "intervalFactor": 2, "legendFormat": "max", "refId": "A", "step": 10}, {"expr": "avg(tikv_engine_get_micro_seconds{db=\"$db\",type=\"get_percentile99\"})", "intervalFactor": 2, "legendFormat": "99%", "refId": "B", "step": 10}, {"expr": "avg(tikv_engine_get_micro_seconds{db=\"$db\",type=\"get_percentile95\"})", "intervalFactor": 2, "legendFormat": "95%", "refId": "C", "step": 10}, {"expr": "avg(tikv_engine_get_micro_seconds{db=\"$db\",type=\"get_average\"})", "intervalFactor": 2, "legendFormat": "avg", "refId": "D", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Get Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "µs", "label": null, "logBase": 10, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 1, "id": 129, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_engine_locate{db=\"$db\", type=\"number_db_seek\"}[1m]))", "intervalFactor": 2, "legendFormat": "seek", "metric": "", "refId": "A", "step": 10}, {"expr": "sum(rate(tikv_engine_locate{db=\"$db\", type=\"number_db_seek_found\"}[1m]))", "intervalFactor": 2, "legendFormat": "seek_found", "metric": "", "refId": "B", "step": 10}, {"expr": "sum(rate(tikv_engine_locate{db=\"$db\", type=\"number_db_next\"}[1m]))", "intervalFactor": 2, "legendFormat": "next", "metric": "", "refId": "C", "step": 10}, {"expr": "sum(rate(tikv_engine_locate{db=\"$db\", type=\"number_db_next_found\"}[1m]))", "intervalFactor": 2, "legendFormat": "next_found", "metric": "", "refId": "D", "step": 10}, {"expr": "sum(rate(tikv_engine_locate{db=\"$db\", type=\"number_db_prev\"}[1m]))", "intervalFactor": 2, "legendFormat": "prev", "metric": "", "refId": "E", "step": 10}, {"expr": "sum(rate(tikv_engine_locate{db=\"$db\", type=\"number_db_prev_found\"}[1m]))", "intervalFactor": 2, "legendFormat": "prev_found", "metric": "", "refId": "F", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Seek Operations", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 0, "id": 125, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "minSpan": null, "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(tikv_engine_seek_micro_seconds{db=\"$db\",type=\"seek_max\"})", "intervalFactor": 2, "legendFormat": "max", "refId": "A", "step": 10}, {"expr": "avg(tikv_engine_seek_micro_seconds{db=\"$db\",type=\"seek_percentile99\"})", "intervalFactor": 2, "legendFormat": "99%", "refId": "B", "step": 10}, {"expr": "avg(tikv_engine_seek_micro_seconds{db=\"$db\",type=\"seek_percentile95\"})", "intervalFactor": 2, "legendFormat": "95%", "refId": "C", "step": 10}, {"expr": "avg(tikv_engine_seek_micro_seconds{db=\"$db\",type=\"seek_average\"})", "intervalFactor": 2, "legendFormat": "avg", "refId": "D", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Seek <PERSON>", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "µs", "label": null, "logBase": 10, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 1, "id": 139, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_engine_write_served{db=\"$db\", type=~\"write_done_by_self|write_done_by_other\"}[1m]))", "intervalFactor": 2, "legendFormat": "done", "refId": "A", "step": 10}, {"expr": "sum(rate(tikv_engine_write_served{db=\"$db\", type=\"write_timeout\"}[1m]))", "intervalFactor": 2, "legendFormat": "timeout", "refId": "B", "step": 10}, {"expr": "sum(rate(tikv_engine_write_served{db=\"$db\", type=\"write_with_wal\"}[1m]))", "intervalFactor": 2, "legendFormat": "with_wal", "refId": "C", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Write Operations", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 0, "id": 126, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "minSpan": null, "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(tikv_engine_write_micro_seconds{db=\"$db\",type=\"write_max\"})", "intervalFactor": 2, "legendFormat": "max", "refId": "A", "step": 10}, {"expr": "avg(tikv_engine_write_micro_seconds{db=\"$db\",type=\"write_percentile99\"})", "intervalFactor": 2, "legendFormat": "99%", "refId": "B", "step": 10}, {"expr": "avg(tikv_engine_write_micro_seconds{db=\"$db\",type=\"write_percentile95\"})", "intervalFactor": 2, "legendFormat": "95%", "refId": "C", "step": 10}, {"expr": "avg(tikv_engine_write_micro_seconds{db=\"$db\",type=\"write_average\"})", "intervalFactor": 2, "legendFormat": "avg", "refId": "D", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Write Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "µs", "label": null, "logBase": 10, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 1, "id": 137, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_engine_wal_file_synced{db=\"$db\"}[1m]))", "intervalFactor": 2, "legendFormat": "sync", "metric": "", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "WAL Sync Operations", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 0, "id": 135, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "minSpan": 6, "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(tikv_engine_wal_file_sync_micro_seconds{db=\"$db\",type=\"wal_file_sync_max\"})", "intervalFactor": 2, "legendFormat": "max", "refId": "A", "step": 10}, {"expr": "avg(tikv_engine_wal_file_sync_micro_seconds{db=\"$db\",type=\"wal_file_sync_percentile99\"})", "intervalFactor": 2, "legendFormat": "99%", "refId": "B", "step": 10}, {"expr": "avg(tikv_engine_wal_file_sync_micro_seconds{db=\"$db\",type=\"wal_file_sync_percentile95\"})", "intervalFactor": 2, "legendFormat": "95%", "refId": "C", "step": 10}, {"expr": "avg(tikv_engine_wal_file_sync_micro_seconds{db=\"$db\",type=\"wal_file_sync_average\"})", "intervalFactor": 2, "legendFormat": "avg", "refId": "D", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "WAL Sync Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "µs", "label": null, "logBase": 10, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 1, "id": 128, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_engine_event_total{db=\"$db\"}[1m])) by (type)", "intervalFactor": 2, "legendFormat": "{{type}}", "metric": "tikv_engine_event_total", "refId": "B", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Compaction Operations", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 0, "id": 136, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "minSpan": null, "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(tikv_engine_compaction_time{db=\"$db\",type=\"compaction_time_max\"})", "intervalFactor": 2, "legendFormat": "max", "metric": "", "refId": "A", "step": 10}, {"expr": "avg(tikv_engine_compaction_time{db=\"$db\",type=\"compaction_time_percentile99\"})", "intervalFactor": 2, "legendFormat": "99%", "refId": "B", "step": 10}, {"expr": "avg(tikv_engine_compaction_time{db=\"$db\",type=\"compaction_time_percentile95\"})", "intervalFactor": 2, "legendFormat": "95%", "refId": "C", "step": 10}, {"expr": "avg(tikv_engine_compaction_time{db=\"$db\",type=\"compaction_time_average\"})", "intervalFactor": 2, "legendFormat": "avg", "refId": "D", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Compaction Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "µs", "label": null, "logBase": 10, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 1, "id": 140, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(rate(tikv_engine_sst_read_micros{db=\"$db\", type=\"sst_read_micros_max\"}[1m]))", "intervalFactor": 2, "legendFormat": "max", "metric": "", "refId": "A", "step": 10}, {"expr": "avg(rate(tikv_engine_sst_read_micros{db=\"$db\", type=\"sst_read_micros_percentile99\"}[1m]))", "intervalFactor": 2, "legendFormat": "99%", "metric": "", "refId": "B", "step": 10}, {"expr": "avg(rate(tikv_engine_sst_read_micros{db=\"$db\", type=\"sst_read_micros_percentile95\"}[1m]))", "intervalFactor": 2, "legendFormat": "95%", "metric": "", "refId": "C", "step": 10}, {"expr": "avg(rate(tikv_engine_sst_read_micros{db=\"$db\", type=\"sst_read_micros_average\"}[1m]))", "intervalFactor": 2, "legendFormat": "avg", "metric": "", "refId": "D", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "SST Read Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ms", "label": null, "logBase": 10, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 1, "id": 87, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(rate(tikv_engine_write_stall{db=\"$db\", type=\"write_stall_max\"}[1m]))", "intervalFactor": 2, "legendFormat": "max", "metric": "", "refId": "A", "step": 10}, {"expr": "avg(rate(tikv_engine_write_stall{db=\"$db\", type=\"write_stall_percentile99\"}[1m]))", "intervalFactor": 2, "legendFormat": "99%", "metric": "", "refId": "B", "step": 10}, {"expr": "avg(rate(tikv_engine_write_stall{db=\"$db\", type=\"write_stall_percentile95\"}[1m]))", "intervalFactor": 2, "legendFormat": "95%", "metric": "", "refId": "C", "step": 10}, {"expr": "avg(rate(tikv_engine_write_stall{db=\"$db\", type=\"write_stall_average\"}[1m]))", "intervalFactor": 2, "legendFormat": "avg", "metric": "", "refId": "D", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Write Stall Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ms", "label": null, "logBase": 10, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 1, "id": 103, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(tikv_engine_memory_bytes{db=\"$db\", type=\"mem-tables\"}) by (cf)", "intervalFactor": 2, "legendFormat": "{{cf}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Memtable Size", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 0, "id": 88, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "minSpan": null, "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_engine_memtable_efficiency{db=\"$db\", type=\"memtable_hit\"}[1m])) / (sum(rate(tikv_engine_memtable_efficiency{db=\"$db\", type=\"memtable_hit\"}[1m])) + sum(rate(tikv_engine_memtable_efficiency{db=\"$db\", type=\"memtable_miss\"}[1m])))", "intervalFactor": 2, "legendFormat": "hit", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Memtable Hit", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": false, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 1, "id": 102, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(tikv_engine_block_cache_size_bytes{db=\"$db\"}) by(cf)", "intervalFactor": 2, "legendFormat": "{{cf}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "<PERSON> <PERSON>", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 0, "id": 80, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "minSpan": 6, "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_engine_cache_efficiency{db=\"$db\", type=\"block_cache_hit\"}[1m])) / (sum(rate(tikv_engine_cache_efficiency{db=\"$db\", type=\"block_cache_hit\"}[1m])) + sum(rate(tikv_engine_cache_efficiency{db=\"$db\", type=\"block_cache_miss\"}[1m])))", "intervalFactor": 2, "legendFormat": "all", "metric": "", "refId": "A", "step": 10}, {"expr": "sum(rate(tikv_engine_cache_efficiency{db=\"$db\", type=\"block_cache_data_hit\"}[1m])) / (sum(rate(tikv_engine_cache_efficiency{db=\"$db\", type=\"block_cache_data_hit\"}[1m])) + sum(rate(tikv_engine_cache_efficiency{db=\"$db\", type=\"block_cache_data_miss\"}[1m])))", "intervalFactor": 2, "legendFormat": "data", "metric": "", "refId": "D", "step": 10}, {"expr": "sum(rate(tikv_engine_cache_efficiency{db=\"$db\", type=\"block_cache_filter_hit\"}[1m])) / (sum(rate(tikv_engine_cache_efficiency{db=\"$db\", type=\"block_cache_filter_hit\"}[1m])) + sum(rate(tikv_engine_cache_efficiency{db=\"$db\", type=\"block_cache_filter_miss\"}[1m])))", "intervalFactor": 2, "legendFormat": "filter", "metric": "", "refId": "B", "step": 10}, {"expr": "sum(rate(tikv_engine_cache_efficiency{db=\"$db\", type=\"block_cache_index_hit\"}[1m])) / (sum(rate(tikv_engine_cache_efficiency{db=\"$db\", type=\"block_cache_index_hit\"}[1m])) + sum(rate(tikv_engine_cache_efficiency{db=\"$db\", type=\"block_cache_index_miss\"}[1m])))", "intervalFactor": 2, "legendFormat": "index", "metric": "", "refId": "C", "step": 10}, {"expr": "sum(rate(tikv_engine_bloom_efficiency{db=\"$db\", type=\"bloom_prefix_useful\"}[1m])) / sum(rate(tikv_engine_bloom_efficiency{db=\"$db\", type=\"bloom_prefix_checked\"}[1m]))", "intervalFactor": 2, "legendFormat": "bloom prefix", "metric": "", "refId": "E", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "<PERSON> <PERSON>", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": false, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 0, "height": "", "id": 467, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_engine_flow_bytes{db=\"$db\", type=\"block_cache_byte_read\"}[1m]))", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "total_read", "refId": "A", "step": 10}, {"expr": "sum(rate(tikv_engine_flow_bytes{db=\"$db\", type=\"block_cache_byte_write\"}[1m]))", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "total_written", "refId": "C", "step": 10}, {"expr": "sum(rate(tikv_engine_cache_efficiency{db=\"$db\", type=\"block_cache_data_bytes_insert\"}[1m]))", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "data_insert", "metric": "", "refId": "D", "step": 10}, {"expr": "sum(rate(tikv_engine_cache_efficiency{db=\"$db\", type=\"block_cache_filter_bytes_insert\"}[1m]))", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "filter_insert", "metric": "", "refId": "B", "step": 10}, {"expr": "sum(rate(tikv_engine_cache_efficiency{db=\"$db\", type=\"block_cache_filter_bytes_evict\"}[1m]))", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "filter_evict", "metric": "", "refId": "E", "step": 10}, {"expr": "sum(rate(tikv_engine_cache_efficiency{db=\"$db\", type=\"block_cache_index_bytes_insert\"}[1m]))", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "index_insert", "metric": "", "refId": "F", "step": 10}, {"expr": "sum(rate(tikv_engine_cache_efficiency{db=\"$db\", type=\"block_cache_index_bytes_evict\"}[1m]))", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "index_evict", "metric": "", "refId": "G", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Block Cache Flow", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "Bps", "label": null, "logBase": 10, "max": null, "min": "0", "show": true}, {"format": "none", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 1, "id": 468, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_engine_cache_efficiency{db=\"$db\", type=\"block_cache_add\"}[1m]))", "intervalFactor": 2, "legendFormat": "total_add", "metric": "", "refId": "A", "step": 10}, {"expr": "sum(rate(tikv_engine_cache_efficiency{db=\"$db\", type=\"block_cache_data_add\"}[1m]))", "intervalFactor": 2, "legendFormat": "data_add", "metric": "", "refId": "C", "step": 10}, {"expr": "sum(rate(tikv_engine_cache_efficiency{db=\"$db\", type=\"block_cache_filter_add\"}[1m]))", "intervalFactor": 2, "legendFormat": "filter_add", "metric": "", "refId": "D", "step": 10}, {"expr": "sum(rate(tikv_engine_cache_efficiency{db=\"$db\", type=\"block_cache_index_add\"}[1m]))", "intervalFactor": 2, "legendFormat": "index_add", "metric": "", "refId": "E", "step": 10}, {"expr": "sum(rate(tikv_engine_cache_efficiency{db=\"$db\", type=\"block_cache_add_failures\"}[1m]))", "intervalFactor": 2, "legendFormat": "add_failures", "metric": "", "refId": "B", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Block Cache Operations", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 0, "height": "", "id": 132, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_engine_flow_bytes{db=\"$db\", type=\"keys_read\"}[1m]))", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "read", "refId": "B", "step": 10}, {"expr": "sum(rate(tikv_engine_flow_bytes{db=\"$db\", type=\"keys_written\"}[1m]))", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "written", "refId": "C", "step": 10}, {"expr": "sum(rate(tikv_engine_compaction_num_corrupt_keys{db=\"$db\"}[1m]))", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "corrupt", "metric": "", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Keys Flow", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 1, "id": 131, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(tikv_engine_estimate_num_keys{db=\"$db\"}) by (cf)", "hide": false, "intervalFactor": 2, "legendFormat": "{{cf}}", "metric": "tikv_engine_estimate_num_keys", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Total Keys", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 0, "height": "", "id": 85, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_engine_flow_bytes{db=\"$db\", type=\"bytes_read\"}[1m]))", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "get", "refId": "A", "step": 10}, {"expr": "sum(rate(tikv_engine_flow_bytes{db=\"$db\", type=\"iter_bytes_read\"}[1m]))", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "scan", "refId": "C", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Read Flow", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "Bps", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 0, "id": 133, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "minSpan": 6, "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(tikv_engine_bytes_per_read{db=\"$db\",type=\"bytes_per_read_max\"})", "intervalFactor": 2, "legendFormat": "max", "refId": "A", "step": 10}, {"expr": "avg(tikv_engine_bytes_per_read{db=\"$db\",type=\"bytes_per_read_percentile99\"})", "intervalFactor": 2, "legendFormat": "99%", "refId": "B", "step": 10}, {"expr": "avg(tikv_engine_bytes_per_read{db=\"$db\",type=\"bytes_per_read_percentile95\"})", "intervalFactor": 2, "legendFormat": "95%", "refId": "C", "step": 10}, {"expr": "avg(tikv_engine_bytes_per_read{db=\"$db\",type=\"bytes_per_read_average\"})", "intervalFactor": 2, "legendFormat": "avg", "refId": "D", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Bytes / Read", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "decbytes", "label": null, "logBase": 10, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 1, "height": "", "id": 86, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_engine_flow_bytes{db=\"$db\", type=\"wal_file_bytes\"}[1m]))", "hide": false, "intervalFactor": 2, "legendFormat": "wal", "refId": "C", "step": 10}, {"expr": "sum(rate(tikv_engine_flow_bytes{db=\"$db\", type=\"bytes_written\"}[1m]))", "hide": false, "intervalFactor": 2, "legendFormat": "write", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Write Flow", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "Bps", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 0, "id": 134, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "minSpan": 6, "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(tikv_engine_bytes_per_write{db=\"$db\",type=\"bytes_per_write_max\"})", "intervalFactor": 2, "legendFormat": "max", "refId": "A", "step": 10}, {"expr": "avg(tikv_engine_bytes_per_write{db=\"$db\",type=\"bytes_per_write_percentile99\"})", "intervalFactor": 2, "legendFormat": "99%", "refId": "B", "step": 10}, {"expr": "avg(tikv_engine_bytes_per_write{db=\"$db\",type=\"bytes_per_write_percentile95\"})", "intervalFactor": 2, "legendFormat": "95%", "refId": "C", "step": 10}, {"expr": "avg(tikv_engine_bytes_per_write{db=\"$db\",type=\"bytes_per_write_average\"})", "intervalFactor": 2, "legendFormat": "avg", "refId": "D", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Bytes / Write", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "decbytes", "label": null, "logBase": 10, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 1, "id": 90, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_engine_compaction_flow_bytes{db=\"$db\", type=\"bytes_read\"}[1m]))", "hide": false, "intervalFactor": 2, "legendFormat": "read", "refId": "A", "step": 10}, {"expr": "sum(rate(tikv_engine_compaction_flow_bytes{db=\"$db\", type=\"bytes_written\"}[1m]))", "hide": false, "intervalFactor": 2, "legendFormat": "written", "refId": "C", "step": 10}, {"expr": "sum(rate(tikv_engine_flow_bytes{db=\"$db\", type=\"flush_write_bytes\"}[1m]))", "hide": false, "intervalFactor": 2, "legendFormat": "flushed", "refId": "B", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Compaction Flow", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "Bps", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "Bps", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 1, "id": 127, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_engine_pending_compaction_bytes{db=\"$db\"}[1m])) by (cf)", "hide": false, "intervalFactor": 2, "legendFormat": "{{cf}}", "metric": "tikv_engine_pending_compaction_bytes", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Compaction Pending  Bytes", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "Bps", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 1, "id": 518, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_engine_read_amp_flow_bytes{db=\"$db\", type=\"read_amp_total_read_bytes\"}[1m])) by (job) / sum(rate(tikv_engine_read_amp_flow_bytes{db=\"$db\", type=\"read_amp_estimate_useful_bytes\"}[1m])) by (job)", "hide": false, "intervalFactor": 2, "legendFormat": "{{job}}", "metric": "", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Read Amplication", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 1, "id": 863, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(tikv_engine_compression_ratio{db=\"$db\"}) by (level)", "hide": false, "intervalFactor": 2, "legendFormat": "level - {{level}}", "metric": "", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Compression Ratio", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 1, "id": 516, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "tikv_engine_num_snapshots{db=\"$db\"}", "hide": false, "intervalFactor": 2, "legendFormat": "{{job}}", "metric": "", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Number of Snapshots", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 1, "id": 517, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "tikv_engine_oldest_snapshot_duration{db=\"$db\"}", "hide": false, "intervalFactor": 2, "legendFormat": "{{job}}", "metric": "tikv_engine_oldest_snapshot_duration", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Oldest Snapshots Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}]}], "repeat": "db", "repeatIteration": null, "repeatRowId": null, "showTitle": true, "title": "Rocksdb - $db", "titleSize": "h6"}, {"collapse": true, "height": "300", "panels": [{"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 1, "id": 95, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_grpc_msg_duration_seconds_count[1m])) by (type)", "intervalFactor": 2, "legendFormat": "{{type}}", "metric": "tikv_grpc_msg_duration_seconds_bucket", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "grpc message count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 1, "id": 107, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_grpc_msg_fail_total[1m])) by (type)", "intervalFactor": 2, "legendFormat": "{{type}}", "metric": "tikv_grpc_msg_fail_total", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "grpc message failed", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 1, "id": 97, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.8, sum(rate(tikv_grpc_msg_duration_seconds_bucket[1m])) by (le, type))", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "80% grpc messge duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 10, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 1, "id": 98, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_grpc_msg_duration_seconds_bucket[1m])) by (le, type))", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "99% grpc messge duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 10, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}], "repeat": null, "repeatIteration": null, "repeatRowId": null, "showTitle": true, "title": "Grpc", "titleSize": "h6"}, {"collapse": true, "height": "300", "panels": [{"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 1, "id": 1069, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 350, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_pd_request_duration_seconds_count[1m])) by (type)", "intervalFactor": 2, "legendFormat": "{{ type }}", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "PD requests", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 1, "id": 1070, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 350, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_pd_request_duration_seconds_sum[1m])) by (type) / sum(rate(tikv_pd_request_duration_seconds_count[1m])) by (type)", "intervalFactor": 2, "legendFormat": "{{ type }}", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "PD request duration (average)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 1, "id": 1215, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 350, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_pd_heartbeat_message_total[1m])) by (type)", "intervalFactor": 2, "legendFormat": "{{ type }}", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "PD heartbeats", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TEST-CLUSTER}", "decimals": 1, "fill": 1, "id": 1396, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 350, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_pd_validate_peer_total[1m])) by (type)", "intervalFactor": 2, "legendFormat": "{{ type }}", "metric": "", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "PD validate peers", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}], "repeat": null, "repeatIteration": null, "repeatRowId": null, "showTitle": true, "title": "PD", "titleSize": "h6"}], "schemaVersion": 14, "style": "dark", "tags": [], "templating": {"list": [{"allValue": null, "current": {}, "datasource": "${DS_TEST-CLUSTER}", "hide": 0, "includeAll": true, "label": "db", "multi": true, "name": "db", "options": [], "query": "label_values(tikv_engine_block_cache_size_bytes, db)", "refresh": 1, "regex": "", "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {}, "datasource": "${DS_TEST-CLUSTER}", "hide": 0, "includeAll": true, "label": "command", "multi": true, "name": "command", "options": [], "query": "label_values(tikv_storage_command_total, type)", "refresh": 1, "regex": "", "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "Test-Cluster-TiKV", "version": 2}