{"__inputs": [{"name": "DS_TIDB-CLUSTER", "label": "tidb-cluster", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}], "__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "4.6.3"}, {"type": "panel", "id": "graph", "name": "Graph", "version": ""}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}], "annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "hideControls": false, "id": null, "links": [], "refresh": false, "rows": [{"collapse": false, "height": "250px", "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${DS_TIDB-CLUSTER}", "description": "QPS of the binlog event that has been received by <PERSON><PERSON><PERSON>", "fill": 1, "id": 1, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": false, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(syncer_binlog_event_count[1m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{job}} - {{type}}", "refId": "A", "step": 20}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "binlog events", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${DS_TIDB-CLUSTER}", "description": "the cost of transforming the binlog event to SQL statements by <PERSON><PERSON><PERSON>", "fill": 1, "id": 9, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": false, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.8, sum(rate(syncer_binlog_event_bucket[1m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{job}} - {{type}}", "refId": "A", "step": 20}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "binlog event transform", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}], "repeat": null, "repeatIteration": null, "repeatRowId": null, "showTitle": false, "title": "Dashboard Row", "titleSize": "h6"}, {"collapse": false, "height": 250, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${DS_TIDB-CLUSTER}", "description": "the cost of executing a transaction on TiDB", "fill": 1, "id": 8, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": false, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(syncer_txn_cost_in_second_bucket[1m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{job}}", "refId": "A", "step": 20}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "transaction latency", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"alert": {"conditions": [{"evaluator": {"params": [1000], "type": "lt"}, "operator": {"type": "and"}, "query": {"params": ["A", "1m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "alerting", "frequency": "60s", "handler": 1, "name": "transaction tps alert", "noDataState": "no_data", "notifications": [{"id": 1}]}, "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${DS_TIDB-CLUSTER}", "description": "TPS of executing a transaction on TiDB", "fill": 1, "id": 7, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": false, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(syncer_txn_cost_in_second_count[1m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{job}}", "refId": "A", "step": 20}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "lt", "value": 1000}], "timeFrom": null, "timeShift": null, "title": "transaction tps", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"alert": {"conditions": [{"evaluator": {"params": [2], "type": "gt"}, "operator": {"type": "and"}, "query": {"datasourceId": 1, "model": {"expr": " syncer_binlog_file{node=\"master\"} - ON(instance, job) syncer_binlog_file{node=\"syncer\"} ", "intervalFactor": 10, "legendFormat": "{{job}}", "refId": "A", "step": 50}, "params": ["A", "5m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "alerting", "frequency": "60s", "handler": 1, "name": "syncer_binlog_file alert", "noDataState": "no_data", "notifications": [{"id": 1}]}, "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${DS_TIDB-CLUSTER}", "description": "the number of different binlog files between the upstream and the downstream in the process of replication; the normal value is 0, which indicates real-time replication; a larger value indicates a larger number of binlog files discrepancy", "fill": 1, "id": 6, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": " syncer_binlog_file{node=\"master\"} - ON(instance, job) syncer_binlog_file{node=\"syncer\"} ", "format": "time_series", "intervalFactor": 10, "legendFormat": "{{job}}", "refId": "A", "step": 100}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 2}], "timeFrom": null, "timeShift": null, "title": "binlog file gap", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${DS_TIDB-CLUSTER}", "description": "it works with `file number of binlog position`. `syncer_binlog_pos{node=\"master\"}` indicates the position of latest binlog position fetched from MySQL, and `syncer_binlog_pos{node=\"syncer\"}` indicates the position of the binlog position that <PERSON><PERSON><PERSON> has replicated.", "fill": 1, "id": 2, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "syncer_binlog_pos{node=\"syncer\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{job}} {{node}}", "refId": "A", "step": 30}, {"expr": "syncer_binlog_pos{node=\"master\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{job}} {{node}}", "refId": "B", "step": 30}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "position of binlog position", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}], "repeat": null, "repeatIteration": null, "repeatRowId": null, "showTitle": false, "title": "Dashboard Row", "titleSize": "h6"}, {"collapse": false, "height": 250, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${DS_TIDB-CLUSTER}", "description": "it works with `position of binlog position`. `syncer_binlog_file{node=\"master\"}` indicates the file number of the latest binlog position fetched from MySQL, and `syncer_binlog_file{node=\"syncer\"}` indicates the file number of the binlog position that <PERSON><PERSON><PERSON> has replicated.", "fill": 1, "id": 4, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "syncer_binlog_file{node=\"master\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{job}} {{node}}", "refId": "A", "step": 30}, {"expr": "syncer_binlog_file{node=\"syncer\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{job}} {{node}}", "refId": "B", "step": 30}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "file number of binlog position", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${DS_TIDB-CLUSTER}", "description": "The total number of SQL statements that Syncer skips when the upstream replicates binlog files with the downstream; you can configure the format of SQL statements skipped by Syncer using the `skip-ddls` and `skip-dmls` parameters in the `syncer.toml` file.", "fill": 1, "id": 3, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(syncer_binlog_skipped_events_total[1m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{job}} {{type}}", "refId": "A", "step": 20}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "binlog skipped events", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${DS_TIDB-CLUSTER}", "description": "the count of jobs that have been added into the execution queue", "fill": 1, "id": 10, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(syncer_add_jobs_total[1m])) by (queueNo)", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "refId": "A", "step": 20}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "execution jobs", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${DS_TIDB-CLUSTER}", "description": "the count of jobs that have been applied into TiDB", "fill": 1, "id": 11, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(syncer_add_jobs_total[1m]) - rate(syncer_finished_jobs_total[1m])) by (queueNo)", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "refId": "A", "step": 20}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "pending  jobs", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}], "repeat": null, "repeatIteration": null, "repeatRowId": null, "showTitle": false, "title": "Dashboard Row", "titleSize": "h6"}], "schemaVersion": 14, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "2019-02-19T03:20:15.463Z", "to": "2019-02-19T09:20:15.463Z"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "<PERSON><PERSON><PERSON>", "version": 14}