{"__inputs": [{"name": "DS_TIDB-CLUSTER", "label": "${DS_TIDB-CLUSTER}", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}], "__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "4.1.2"}, {"type": "panel", "id": "graph", "name": "Graph", "version": ""}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}, {"type": "panel", "id": "singlestat", "name": "Singlestat", "version": ""}, {"type": "panel", "id": "table", "name": "Table", "version": ""}], "annotations": {"list": []}, "editable": true, "gnetId": null, "graphTooltip": 0, "hideControls": false, "id": null, "links": [], "refresh": "30s", "rows": [{"collapse": false, "height": 250, "panels": [{"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "datasource": "${DS_TIDB-CLUSTER}", "decimals": null, "editable": true, "error": false, "format": "bytes", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": false}, "id": 27, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "null", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "span": 4, "sparkline": {"fillColor": "rgba(77, 135, 25, 0.18)", "full": true, "lineColor": "rgb(21, 179, 65)", "show": true}, "targets": [{"expr": "pd_cluster_status{instance=\"$instance\",type=\"storage_capacity\"}", "intervalFactor": 2, "refId": "A", "step": 4}], "thresholds": "", "title": "Storage Capacity", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "datasource": "${DS_TIDB-CLUSTER}", "decimals": 1, "editable": true, "error": false, "format": "bytes", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "hideTimeOverride": false, "id": 28, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "null", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "span": 4, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": true, "lineColor": "rgb(31, 120, 193)", "show": true}, "targets": [{"expr": "pd_cluster_status{instance=\"$instance\",type=\"storage_size\"}", "intervalFactor": 2, "refId": "A", "step": 4}], "thresholds": "", "title": "Current Storage Size", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"columns": [{"text": "Current", "value": "current"}], "datasource": "${DS_TIDB-CLUSTER}", "editable": true, "error": false, "fontSize": "120%", "hideTimeOverride": false, "id": 18, "links": [], "pageSize": null, "repeat": null, "scroll": false, "showHeader": true, "sort": {"col": null, "desc": false}, "span": 4, "styles": [{"dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Metric", "sanitize": false, "type": "string"}, {"colorMode": "cell", "colors": ["rgba(50, 172, 45, 0.97)", "rgba(237, 129, 40, 0.89)", "rgba(245, 54, 54, 0.9)"], "decimals": 0, "pattern": "Current", "thresholds": ["1", "2"], "type": "number", "unit": "short"}], "targets": [{"expr": "pd_cluster_status{instance=\"$instance\",type=\"store_up_count\"}", "interval": "", "intervalFactor": 2, "legendFormat": "Up Stores", "metric": "pd_cluster_status", "refId": "A", "step": 2}, {"expr": "pd_cluster_status{instance=\"$instance\",type=\"store_down_count\"}", "intervalFactor": 2, "legendFormat": "Down Stores", "refId": "B", "step": 2}, {"expr": "pd_cluster_status{instance=\"$instance\",type=\"store_offline_count\"}", "intervalFactor": 2, "legendFormat": "Offline Stores", "refId": "C", "step": 2}, {"expr": "pd_cluster_status{instance=\"$instance\",type=\"store_tombstone_count\"}", "intervalFactor": 2, "legendFormat": "Tombstone Stores", "refId": "D", "step": 2}], "title": "Store Status", "transform": "timeseries_aggregations", "transparent": false, "type": "table"}, {"alert": {"conditions": [{"evaluator": {"params": [0.8], "type": "gt"}, "operator": {"type": "and"}, "query": {"datasourceId": 1, "model": {"expr": "avg(pd_cluster_status{type=\"storage_size\"}) / avg(pd_cluster_status{type=\"storage_capacity\"})", "hide": false, "intervalFactor": 4, "legendFormat": "used ratio", "refId": "B", "step": 4}, "params": ["B", "5m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "alerting", "frequency": "60s", "handler": 1, "message": "Storage used space is above 80%.", "name": "Current Storage Usage alert", "noDataState": "no_data", "notifications": []}, "aliasColors": {}, "bars": false, "datasource": "${DS_TIDB-CLUSTER}", "decimals": 2, "editable": true, "error": false, "fill": 0, "grid": {}, "id": 22, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "used ratio", "yaxis": 2}], "span": 12, "stack": false, "steppedLine": false, "targets": [{"expr": "pd_cluster_status{instance=\"$instance\",type=\"storage_size\"}", "hide": false, "intervalFactor": 2, "legendFormat": "strage size", "refId": "A", "step": 2}, {"expr": "avg(pd_cluster_status{type=\"storage_size\"}) / avg(pd_cluster_status{type=\"storage_capacity\"})", "hide": false, "intervalFactor": 4, "legendFormat": "used ratio", "refId": "B", "step": 4}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0.8}], "timeFrom": null, "timeShift": null, "title": "Current Storage Usage", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "decbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TIDB-CLUSTER}", "editable": true, "error": false, "fill": 0, "grid": {}, "id": 23, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(grpc_server_handling_seconds_bucket{instance=\"$instance\"}[5m])) by (grpc_method, le))", "intervalFactor": 2, "legendFormat": "{{grpc_method}}", "refId": "A", "step": 2}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "99% completed_cmds_duration_seconds", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TIDB-CLUSTER}", "editable": true, "error": false, "fill": 0, "grid": {}, "id": 24, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": true, "targets": [{"expr": "histogram_quantile(0.9999, sum(rate(grpc_server_handling_seconds_sum{instance=\"$instance\"}[5m])) by (grpc_method, le))", "hide": true, "intervalFactor": 2, "legendFormat": "{{grpc_method}}", "refId": "A", "step": 4}, {"expr": "rate(grpc_server_handling_seconds_sum{instance=\"$instance\"}[30s]) / rate(grpc_server_handling_seconds_count{instance=\"$instance\"}[30s])", "intervalFactor": 2, "legendFormat": "{{grpc_method}}", "refId": "B", "step": 2}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "average completed_cmds_duration_seconds", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"alert": {"conditions": [{"evaluator": {"params": [0.2], "type": "gt"}, "operator": {"type": "and"}, "query": {"datasourceId": 1, "model": {"expr": "min(pd_cluster_status{type=\"region_balance_ratio\"})", "hide": true, "intervalFactor": 2, "legendFormat": "ratio", "refId": "B", "step": 4}, "params": ["B", "5m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "alerting", "frequency": "60s", "handler": 1, "message": "Store balance ratio is high", "name": "Region Balance Ratio alert", "noDataState": "no_data", "notifications": []}, "aliasColors": {}, "bars": false, "datasource": "${DS_TIDB-CLUSTER}", "decimals": 4, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 26, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "pd_cluster_status{instance=\"$instance\",type=\"region_balance_ratio\"}", "hide": false, "intervalFactor": 2, "legendFormat": "\bstore balance ratio", "refId": "A", "step": 2}, {"expr": "min(pd_cluster_status{type=\"region_balance_ratio\"})", "hide": true, "intervalFactor": 2, "legendFormat": "ratio", "refId": "B", "step": 4}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0.2}], "timeFrom": null, "timeShift": null, "title": "Region Balance Ratio", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"alert": {"conditions": [{"evaluator": {"params": [0.2], "type": "gt"}, "operator": {"type": "and"}, "query": {"datasourceId": 1, "model": {"expr": "min(pd_cluster_status{type=\"leader_balance_ratio\"})", "hide": true, "intervalFactor": 2, "legendFormat": "ratio", "refId": "B", "step": 4}, "params": ["B", "1m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "alerting", "frequency": "60s", "handler": 1, "message": "Region leader balance ratio is high.", "name": "Leader <PERSON><PERSON> alert", "noDataState": "no_data", "notifications": []}, "aliasColors": {}, "bars": false, "datasource": "${DS_TIDB-CLUSTER}", "decimals": 4, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 25, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "pd_cluster_status{instance=\"$instance\",type=\"leader_balance_ratio\"}", "intervalFactor": 2, "legendFormat": "leader max diff ratio", "refId": "A", "step": 2}, {"expr": "min(pd_cluster_status{type=\"leader_balance_ratio\"})", "hide": true, "intervalFactor": 2, "legendFormat": "ratio", "refId": "B", "step": 4}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0.2}], "timeFrom": null, "timeShift": null, "title": "Leader <PERSON><PERSON>", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}], "repeat": null, "repeatIteration": null, "repeatRowId": null, "showTitle": true, "title": "PD", "titleSize": "h6"}, {"collapse": false, "height": "250px", "panels": [{"aliasColors": {}, "bars": false, "datasource": "${DS_TIDB-CLUSTER}", "editable": true, "error": false, "fill": 1, "grid": {}, "id": 1, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "sort": "current", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 12, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.98, sum(rate(pd_client_request_handle_requests_duration_seconds_bucket[30s])) by (type, le))", "hide": false, "intervalFactor": 2, "legendFormat": "{{type}} 98th percentile", "refId": "A", "step": 2}, {"expr": "avg(rate(pd_client_request_handle_requests_duration_seconds_sum[30s])) by (type) /  avg(rate(pd_client_request_handle_requests_duration_seconds_count[30s])) by (type)", "intervalFactor": 2, "legendFormat": "{{type}} average", "refId": "B", "step": 2}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "handle_requests_duration_seconds", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TIDB-CLUSTER}", "editable": true, "error": false, "fill": 0, "grid": {}, "id": 2, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "minSpan": 12, "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "total", "lines": false}], "span": 12, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(tidb_server_query_total[1m])", "intervalFactor": 2, "legendFormat": "{{instance}} {{type}} {{status}}", "refId": "A", "step": 2}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "QPS", "tooltip": {"msResolution": true, "shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TIDB-CLUSTER}", "editable": true, "error": false, "fill": 1, "grid": {}, "id": 4, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "total", "fill": 0, "lines": false}], "span": 12, "stack": true, "steppedLine": true, "targets": [{"expr": "tidb_server_connections", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A", "step": 2}, {"expr": "sum(tidb_server_connections)", "intervalFactor": 2, "legendFormat": "total", "refId": "B", "step": 2}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Connection Count", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TIDB-CLUSTER}", "decimals": null, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 3, "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": null, "sortDesc": null, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 12, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(irate(tidb_executor_statement_node_total[1m])) by (type)", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 2}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Statement Count", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 10, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"alert": {"conditions": [{"evaluator": {"params": [10], "type": "gt"}, "operator": {"type": "and"}, "query": {"datasourceId": 1, "model": {"expr": "histogram_quantile(0.99, sum(rate(tidb_server_handle_query_duration_seconds_bucket[1m])) by (le, instance))", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A", "step": 2}, "params": ["A", "1m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "alerting", "frequency": "60s", "handler": 1, "message": "Query duration for 99th percentile is high.", "name": "Query Duration 99th percentile alert", "noDataState": "no_data", "notifications": []}, "aliasColors": {}, "bars": false, "datasource": "${DS_TIDB-CLUSTER}", "editable": true, "error": false, "fill": 1, "grid": {}, "id": 5, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tidb_server_handle_query_duration_seconds_bucket[1m])) by (le))", "intervalFactor": 2, "legendFormat": "average", "refId": "B", "step": 2}, {"expr": "histogram_quantile(0.99, sum(rate(tidb_server_handle_query_duration_seconds_bucket[1m])) by (le, instance))", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A", "step": 2}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 10}], "timeFrom": null, "timeShift": null, "title": "Query Duration 99th percentile", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"alert": {"conditions": [{"evaluator": {"params": [1], "type": "gt"}, "operator": {"type": "and"}, "query": {"datasourceId": 1, "model": {"expr": "rate(tidb_server_schema_lease_error_counter[1m])", "intervalFactor": 2, "legendFormat": "{{instance}} {{type}}", "metric": "tidb_server_", "refId": "A", "step": 2}, "params": ["A", "1m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "alerting", "frequency": "60s", "handler": 1, "message": "Schema lease error.", "name": "Schema Lease Error alert", "noDataState": "no_data", "notifications": []}, "aliasColors": {}, "bars": false, "datasource": "${DS_TIDB-CLUSTER}", "decimals": 2, "editable": true, "error": false, "fill": 0, "grid": {}, "id": 6, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(tidb_server_schema_lease_error_counter[1m])", "intervalFactor": 2, "legendFormat": "{{instance}} {{type}}", "metric": "tidb_server_", "refId": "A", "step": 2}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 1}], "timeFrom": null, "timeShift": null, "title": "Schema Lease Error Rate", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}], "repeat": null, "repeatIteration": null, "repeatRowId": null, "showTitle": true, "title": "TiDB", "titleSize": "h6"}, {"collapse": false, "height": 299, "panels": [{"aliasColors": {}, "bars": false, "datasource": "${DS_TIDB-CLUSTER}", "editable": true, "error": false, "fill": 1, "grid": {}, "id": 7, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_scheduler_command_duration_seconds_bucket[1m])) by (le,type))", "intervalFactor": 2, "legendFormat": "{{type}}", "metric": "tikv_scheduler_command_duration_seconds_bucket", "refId": "A", "step": 2}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "99% scheduler command duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TIDB-CLUSTER}", "editable": true, "error": false, "fill": 1, "grid": {}, "id": 8, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.9999, sum(rate(tikv_scheduler_command_duration_seconds_bucket[1m])) by (le,type))", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 2}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "99.99% scheduler command duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TIDB-CLUSTER}", "editable": true, "error": false, "fill": 1, "grid": {}, "id": 9, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(tikv_storage_engine_async_request_duration_seconds_bucket[1m])) by (le, type))", "intervalFactor": 2, "legendFormat": "{{type}}", "metric": "tikv_storage_engine_async_request_duration_seconds_bucket", "refId": "A", "step": 2}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "95% storage async request duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TIDB-CLUSTER}", "editable": true, "error": false, "fill": 1, "grid": {}, "id": 10, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_storage_engine_async_request_duration_seconds_bucket[1m])) by (le, type))", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 2}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "99% storage async request duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TIDB-CLUSTER}", "editable": true, "error": false, "fill": 0, "grid": {}, "id": 11, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_server_report_failure_msg_total[1m])) by (type,instance,job,store_id)", "intervalFactor": 2, "legendFormat": "{{job}}-{{type}}-to-{{store_id}}", "metric": "tikv_server_raft_store_msg_total", "refId": "A", "step": 2}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "server report failure msg", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TIDB-CLUSTER}", "editable": true, "error": false, "fill": 1, "grid": {}, "id": 12, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_raftstore_raft_sent_message_total{type=\"vote\"}[1m])) by (job)", "intervalFactor": 2, "legendFormat": "{{job}}-vote", "refId": "A", "step": 2}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "vote", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TIDB-CLUSTER}", "decimals": 5, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 13, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": false, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_coprocessor_request_duration_seconds_bucket{req!=\"\"}[1m])) by (le,type,req))", "intervalFactor": 2, "legendFormat": "{{type}}-{{req}}", "refId": "A", "step": 2}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "99% coprocessor request duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TIDB-CLUSTER}", "decimals": 5, "editable": true, "error": false, "fill": 1, "grid": {}, "id": 14, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(tikv_coprocessor_request_duration_seconds_bucket{req!=\"\"}[1m])) by (le,type,req))", "intervalFactor": 2, "legendFormat": "{{type}}-{{req}}", "metric": "tikv_coprocessor_request_duration_seconds_bucket", "refId": "A", "step": 2}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "95% coprocessor request duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TIDB-CLUSTER}", "editable": true, "error": false, "fill": 1, "grid": {}, "id": 15, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": false, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 8, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_worker_pending_task_total[1m])) by (name)", "intervalFactor": 2, "legendFormat": "{{name}}", "metric": "tikv_pd_heartbeat_tick_total", "refId": "A", "step": 2}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Pending Task", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TIDB-CLUSTER}", "fill": 1, "id": 16, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 4, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_engine_stall_micro_seconds[30s])) by (job)", "intervalFactor": 2, "legendFormat": "{{job}}", "refId": "A", "step": 2}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "stall", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"alert": {"conditions": [{"evaluator": {"params": [0], "type": "gt"}, "operator": {"type": "and"}, "query": {"datasourceId": 1, "model": {"expr": "sum(rate(tikv_channel_full_total[1m])) by (type, job)", "intervalFactor": 2, "legendFormat": "{{job}}-{{type}}", "metric": "", "refId": "A", "step": 2}, "params": ["A", "1m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "alerting", "frequency": "60s", "handler": 1, "message": "TiKV channel full", "name": "TiKV channel full alert", "noDataState": "no_data", "notifications": []}, "aliasColors": {}, "bars": false, "datasource": "${DS_TIDB-CLUSTER}", "editable": true, "error": false, "fill": 3, "grid": {}, "id": 17, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 5, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_channel_full_total[1m])) by (type, job)", "intervalFactor": 2, "legendFormat": "{{job}}-{{type}}", "metric": "", "refId": "A", "step": 2}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0}], "timeFrom": null, "timeShift": null, "title": "channel full", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TIDB-CLUSTER}", "editable": true, "error": false, "fill": 0, "grid": {}, "id": 20, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "total", "lines": false}], "span": 7, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(tikv_pd_heartbeat_tick_total{type=\"leader\"}) by (instance,job)", "intervalFactor": 2, "legendFormat": "{{instance}}-{{job}}", "metric": "tikv_pd_heartbeat_tick_total", "refId": "A", "step": 2}, {"expr": "sum(tikv_pd_heartbeat_tick_total{type=\"leader\"}) ", "hide": true, "intervalFactor": 2, "legendFormat": "total", "refId": "B", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "leader", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TIDB-CLUSTER}", "editable": true, "error": false, "fill": 1, "grid": {}, "id": 19, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 5, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(tikv_pd_msg_send_duration_seconds_bucket[30s])) by (le))", "intervalFactor": 2, "legendFormat": "", "refId": "A", "step": 2}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "95% send_message_duration_seconds", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_TIDB-CLUSTER}", "editable": true, "error": false, "fill": 0, "grid": {}, "id": 21, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 7, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(tikv_pd_heartbeat_tick_total{type=\"region\"}) by (job,instance)", "intervalFactor": 2, "legendFormat": "{{instance}}-{{job}}", "refId": "A", "step": 2}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "region", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": "", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}], "repeat": null, "repeatIteration": null, "repeatRowId": null, "showTitle": true, "title": "TiKV", "titleSize": "h6"}], "schemaVersion": 14, "style": "dark", "tags": [], "templating": {"list": [{"allValue": null, "current": {}, "datasource": "${DS_TIDB-CLUSTER}", "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "instance", "options": [], "query": "label_values(pd_cluster_status, instance)", "refresh": 1, "regex": "", "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "TiDB Cluster - Overview", "version": 1}