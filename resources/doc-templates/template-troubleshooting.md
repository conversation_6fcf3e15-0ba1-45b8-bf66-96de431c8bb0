---
title: (The same as L1 heading) Such as "Troubleshoot Increased Read and Write Latency" in 59 characters or less. Include the keywords of this document. Test title here https://moz.com/learn/seo/title-tag
summary: Summarize this doc in 115 to 145 characters. Start with an SEO-friendly verb that tells the users what they can get from this doc. For example, "Learn how to troubleshoot your cluster when you encounter xxx". If your intro paragraph describes your article's intent, you can use it here, edited for length.
---

# L1 heading (the same as title in the metadata)

> About this template:
>
> - This document is a template for troubleshooting topics, including how to diagnose and solve problems. You can directly copy and use this template and delete unnecessary annotations. An example of this type of document: [Troubleshoot Increased Read and Write Latency](/troubleshoot-cpu-issues.md).
> - For a new document, please add a link to the appropriate location in the `TOC.md` file (consider where users are most likely to look for this document in the table of contents).
> - The headings within the document cannot skip levels, and try to avoid using level 5 headings.

This document describes how to diagnose [xxx problems] and their possible causes and solutions.

## Common causes

Put the most common causes of the problem in this section to help users prioritize these probable causes.

### Common cause 1

Briefly describe the background information of common cause 1 and the impact on the system.

**Symptom:**

Give the information that helps users determine whether the current problem is caused by common cause 1.

**Solution:**

List the solutions to the problem caused by common cause 1. Use unordered lists or steps to present the information according to whether the solution has a specific order.

If the solution is a series of steps, use an ordered list. For example:

1. The first step
2. The second step
3. The third step

After the solution is provided, ask users to check whether the previous problem is resolved after following the solution. If the problem persists, describe how to proceed.

### Common cause 2

Briefly describe the background information of common cause 2 and the impact on the system.

**Symptom:**

(The same as cause 1)

**Solution:**

(The same as cause 1)

## Other causes

List the less common causes of the problem in this section.

### Cause 1

(The same as the previous section)

### Cause 2

(The same as the previous section)
