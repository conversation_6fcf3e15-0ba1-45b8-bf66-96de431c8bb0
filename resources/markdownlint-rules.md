# Markdown Rules

TiDB documentation is written in Markdown. To ensure the quality and consistent format, you should follow certain Markdown rules when modifying documents. We set a CI check for Markdown files in the docs repository, which is the [markdownlint check](https://github.com/DavidAnson/markdownlint/blob/master/doc/Rules.md).

If you submit a Pull Request (PR) that does not conform to the rules, your PR might **fail to pass the markdownlint check**. In such cases, you might not be able to merge this PR.

If you are not familiar with the related Markdown rules before submitting a PR, and your PR fails to pass the markdownlint check, don't worry. The error message tells you clearly that which line of which file fails to pass a certain rule, so you can update the documents according to the message.

You can also run a local markdownlint check:

```bash
./scripts/markdownlint [FILE...]
```

👇The following table shows the 25 [markdownlint rules](https://github.com/DavidAnson/markdownlint/blob/master/doc/Rules.md) that we set in advance for TiDB documentation, along with straightforward explanations. 🤓A quick glance at the table, and you can surely grasp the basics of our Markdown rules.

| NO. | Rules | Descriptions |
| :--- | :--- | :--- |
| 1 | [MD001 - Heading levels should only increment by one level at a time](https://github.com/<PERSON><PERSON>nson/markdownlint/blob/master/doc/Rules.md#md001---heading-levels-should-only-increment-by-one-level-at-a-time) | Headings should start from the first level. When using multiple heading levels, do not skip any level. For example, a third level heading cannot be used directly under a first level heading; a fourth level heading cannot be used directly under a second level heading. |
| 2 | [MD003 - Heading style](https://github.com/DavidAnson/markdownlint/blob/master/doc/Rules.md#md003---heading-style) | Headings must be written in the ATX style, which means using the `#` characters to specify heading levels. |
| 3 | [MD018 - No space after hash on atx style heading](https://github.com/DavidAnson/markdownlint/blob/master/doc/Rules.md#md018---no-space-after-hash-on-atx-style-heading) | **A single space** must be added between the `#` characters and the heading text. |
| 4 | [MD019 - Multiple spaces after hash on atx style heading](https://github.com/DavidAnson/markdownlint/blob/master/doc/Rules.md#md019---multiple-spaces-after-hash-on-atx-style-heading) | Only **a single space** can be added between the `#` characters and the heading text. Multiple spaces are not allowed. |
| 5 | [MD023 - Headings must start at the beginning of the line](https://github.com/DavidAnson/markdownlint/blob/master/doc/Rules.md#md023---headings-must-start-at-the-beginning-of-the-line) | Headings must start at the beginning of the lines. No space is added before the `#` characters of headings. |
| 6 | [MD026 - Trailing punctuation in heading](https://github.com/DavidAnson/markdownlint/blob/master/doc/Rules.md#md026---trailing-punctuation-in-heading) | Only specific punctuation can be used at the end of headings: the question mark `?`, the backtick `` ` ``, the double quotation mark `"`, and the single quotation mark `'`. Other punctuation cannot be used at the end of headings, including the colon `:`, the comma `,`, the period `.`, and the exclamation mark `!`. |
| 7 | [MD022 - Headings should be surrounded by blank lines](https://github.com/DavidAnson/markdownlint/blob/master/doc/Rules.md#md022---headings-should-be-surrounded-by-blank-lines) | Headings should have a blank line both before and after. |
| 8 | [MD024 - Multiple headings with the same content](https://github.com/DavidAnson/markdownlint/blob/master/doc/Rules.md#md024---multiple-headings-with-the-same-content) | Consecutive headings with the same content are not allowed in the documents. For example, if the first level heading is `# TiDB Architecture`, the second level heading that comes after it cannot be `## TiDB Architecture`. However, if the two headings are not consecutive, they can have the same content. |
| 9 | [MD025 - Multiple top level headings in the same document](https://github.com/DavidAnson/markdownlint/blob/master/doc/Rules.md#md025---multiple-top-level-headings-in-the-same-document) | Only one top level heading is allowed in each document. The metadata before the top level heading, which specifies `title` and `category`, does not violate this rule. |
| 10 | [MD041 - First line in file should be a top level heading](https://github.com/DavidAnson/markdownlint/blob/master/doc/Rules.md#md041---first-line-in-file-should-be-a-top-level-heading) | The first line in file should be a top level heading. The CI check ignores the metadata in the first few lines of the file and examines if a top level heading comes after the metadata. |
| 11 | [MD007 - Unordered list indentation](https://github.com/DavidAnson/markdownlint/blob/master/doc/Rules.md#md007---unordered-list-indentation) | In general, list items are indented by 4 spaces in all the `.md` files. The only exception is the `TOC.md` file, in which list items are indented by 2 spaces. |
| 12 | [MD010 - Hard tabs](https://github.com/DavidAnson/markdownlint/blob/master/doc/Rules.md#md010---hard-tabs) | **Hard tab characters** are not allowed in the documents, including in code blocks. If you need to indent, please use **spaces** instead. |
| 13 | [MD012 - Multiple consecutive blank lines](https://github.com/DavidAnson/markdownlint/blob/master/doc/Rules.md#md012---multiple-consecutive-blank-lines) | Multiple consecutive blank lines are not allowed. |
| 14 | [MD027 - Multiple spaces after blockquote symbol](https://github.com/DavidAnson/markdownlint/blob/master/doc/Rules.md#md027---multiple-spaces-after-blockquote-symbol) | Multiple spaces after the blockquote symbol `>` are not allowed. Only **one** space can be used, followed by the quote content. |
| 15 | [MD029 - Ordered list item prefix](https://github.com/DavidAnson/markdownlint/blob/master/doc/Rules.md#md029---ordered-list-item-prefix) | When you use an ordered list, the item prefix must begin with `1.` and increase in numerical order. |
| 16 | [MD030 - Spaces after list markers](https://github.com/DavidAnson/markdownlint/blob/master/doc/Rules.md#md030---spaces-after-list-markers) | When you use a list, only **one space** is added between the list marker (`+`, `-`, `*` or numbers) and the text of the list item. |
| 17 | [MD032 - Lists should be surrounded by blank lines](https://github.com/DavidAnson/markdownlint/blob/master/doc/Rules.md#md032---lists-should-be-surrounded-by-blank-lines) | Lists (of any kind) must have a blank line both before and after. |
| 18 | [MD031 - Fenced code blocks should be surrounded by blank lines](https://github.com/DavidAnson/markdownlint/blob/master/doc/Rules.md#md031---fenced-code-blocks-should-be-surrounded-by-blank-lines) | Code blocks must have a blank line both before and after.|
| 19 | [MD034 - Bare URL used](https://github.com/DavidAnson/markdownlint/blob/master/doc/Rules.md#md034---bare-url-used) | Bare URL is not allowed in the documents. If you want users to directly click and open that URL, add a pair of angle brackets around the URL (`<URL>`). If you have to use a bare URL under special circumstances, and you do not require users to click and open it, add a pair of backticks around the URL (`` `URL` ``). |
| 20 | [MD037 - Spaces inside emphasis markers](https://github.com/DavidAnson/markdownlint/blob/master/doc/Rules.md#md037---spaces-inside-emphasis-markers) | When emphasis markers (bold, italic) are used, extra spaces are not allowed inside the emphasis markers. For example, `** bold text **` is not allowed. |
| 21 | [MD038 - Spaces inside code span elements](https://github.com/DavidAnson/markdownlint/blob/master/doc/Rules.md#md038---spaces-inside-code-span-elements) | When backticks are used to enclose code span elements, extra spaces are not allowed in the code span. For example, `` ` example text ` `` is not allowed. |
| 22 | [MD039 - Spaces inside link text](https://github.com/DavidAnson/markdownlint/blob/master/doc/Rules.md#md039---spaces-inside-link-text) | Extra spaces are not allowed on both sides of the link text. For example, `[ a link ](https://www.example.com/)` is not allowed. |
| 23 | [MD042 - No empty links](https://github.com/DavidAnson/markdownlint/blob/master/doc/Rules.md#md042---no-empty-links) | Links must have a destination. For example, `[empty link 1]()` and `[empty link 2](#)` are not allowed. |
| 24 | [MD045 - Images should have alternate text (alt text)](https://github.com/DavidAnson/markdownlint/blob/master/doc/Rules.md#md045---images-should-have-alternate-text-alt-text) | Images should have alternate text (alt text). Alt text is written in `[]` of `![]()`. It describes the image for people who fails to load the image. |
| 25 | [MD046 - Code block style](https://github.com/DavidAnson/markdownlint/blob/master/doc/Rules.md#md046---code-block-style) | Use **three backticks** ` ``` ` to enclose code blocks in the documents. Do **not** use **four-space indentation** to indicate code blocks. |
